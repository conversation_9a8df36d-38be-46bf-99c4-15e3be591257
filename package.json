{"name": "recargasapp", "version": "0.0.1", "description": "Aplicación de recargas de Loxasoluciones", "productName": "Recargas App", "author": "dfcarrera79 <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "axios": "^1.2.1", "jspdf": "^2.5.2", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "pinia": "^2.0.11", "quasar": "^2.17.1", "vue": "^3.4.18", "vue-router": "^4.0.12", "vue-router-multiguard": "^1.0.3"}, "devDependencies": {"@electron/packager": "^18.3.6", "@quasar/app-vite": "^1.10.2", "@types/node": "^20.14.8", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "autoprefixer": "^10.4.2", "electron": "^34.0.1", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "~5.5.4", "vite-plugin-checker": "^0.8.0", "vue-tsc": "^2.0.29"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}