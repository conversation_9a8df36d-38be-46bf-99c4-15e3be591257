import { useAppStore } from '../stores/app-store';
import multiguard from 'vue-router-multiguard';
import {
  RouteRecordRaw,
  NavigationGuardNext,
  RouteLocationNormalized,
} from 'vue-router';

// const estaLogeado = (
//   to: RouteLocationNormalized,
//   from: RouteLocationNormalized,
//   next: NavigationGuardNext
// ) => {
//   const appStore = useAppStore();
//   if (appStore.estaLogeado === false) {
//     return next({
//       path: '/login',
//       query: { redirect: to.fullPath },
//     });
//   }
//   return next();
// };

const verificarAcceso = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext
) => {
  const appStore = useAppStore();

  if (!appStore.estaLogeado) {
    return next({
      path: '/login',
      query: { redirect: to.fullPath },
    });
  }

  // Rutas restringidas para usuarios NO autorizados
  const rutasRestringidas = [
    '/recargas',
    '/recargas_sin_facturar',
    '/reimprimir_recibos',
  ];

  if (rutasRestringidas.includes(to.path) && appStore.APP_ADMIN === 1) {
    return next('/'); // Redirige a home si no tiene permiso
  }

  // Restringir acceso a "/conciliaciones" si no es usuario autorizado
  if (to.path === '/conciliaciones' && appStore.APP_ADMIN === 0) {
    return next('/');
  }

  return next();
};

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/IndexPage.vue'),
        beforeEnter: multiguard([verificarAcceso]),
      },
    ],
  },
  {
    path: '/recargas',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/Recargas/RecargasPage.vue'),
        beforeEnter: multiguard([verificarAcceso]),
      },
    ],
  },
  {
    path: '/recargas_sin_facturar',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/Recargas/RecargasSinFacturarPage.vue'),
        beforeEnter: multiguard([verificarAcceso]),
      },
    ],
  },
  {
    path: '/reimprimir_recibos',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/Recargas/ReimprimirRecibosPage.vue'),
        beforeEnter: multiguard([verificarAcceso]),
      },
    ],
  },
  {
    path: '/conciliaciones',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/Conciliaciones/ConciliacionesPage.vue'),
        beforeEnter: multiguard([verificarAcceso]),
      },
    ],
  },
  {
    path: '/cambiar_clave',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/CambiarClavePage.vue'),
        beforeEnter: multiguard([verificarAcceso]),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('layouts/LoginLayout.vue'),
    children: [{ path: '', component: () => import('pages/LoginPage.vue') }],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
