export const emailRule = [
  (v: string) =>
    !v ||
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) ||
    'Formato de correo electrónico inválido',
];

export const celularRule = [
  (val: string) =>
    !val || (val.length === 10 && /^[0-9]+$/.test(val)) || 'Ingrese 10 dígitos',
];

// export const cedRucRule = [(val: string) => !val || (val.length === 10 && val !== '9999999999') || (val.length === 13 && val.endsWith('001') && val !== '9999999999999') || 'Por favor ingrese una cédula o RUC válido'];

export const cedRucRule = [
  (val: string) =>
    !val ||
    val.length === 10 ||
    val.length === 13 ||
    'El número debe tener 10 o 13 dígitos',
];
