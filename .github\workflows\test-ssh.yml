name: Deploy Quasar App to GCP

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.GCP_SERVER_SSH_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          # Configurar SSH
          cat > ~/.ssh/config << 'SSHCONFIG'
          Host ${{ secrets.GCP_SERVER_HOST }}
            HostName ${{ secrets.GCP_SERVER_HOST }}
            User ${{ secrets.GCP_SERVER_USER }}
            IdentityFile ~/.ssh/id_ed25519
            StrictHostKeyChecking no
            UserKnownHostsFile /dev/null
            LogLevel ERROR
          SSHCONFIG
          chmod 600 ~/.ssh/config

      - name: Create deployment directory on server
        run: |
          ssh ${{ secrets.GCP_SERVER_HOST }} '
            mkdir -p ~/quasar-deploy
          '

      - name: Copy project files to server
        run: |
          scp -r ./* ${{ secrets.GCP_SERVER_HOST }}:~/quasar-deploy/

      - name: Deploy application
        run: |
          ssh ${{ secrets.GCP_SERVER_HOST }} '
            cd ~/quasar-deploy

            # Detener y eliminar contenedor existente si existe
            if [ $(sudo docker ps -q -f name=recargasapp-nginx) ]; then
              echo "Deteniendo contenedor existente..."
              sudo docker stop recargasapp-nginx
            fi

            if [ $(sudo docker ps -aq -f name=recargasapp-nginx) ]; then
              echo "Eliminando contenedor existente..."
              sudo docker rm recargasapp-nginx
            fi

            # Eliminar imagen anterior si existe
            if [ $(sudo docker images -q recargasapp-nginx) ]; then
              echo "Eliminando imagen anterior..."
              sudo docker rmi recargasapp-nginx
            fi

            # Construir nueva imagen
            echo "Construyendo nueva imagen..."
            sudo docker build -t recargasapp-nginx .

            # Ejecutar nuevo contenedor
            echo "Ejecutando nuevo contenedor..."
            sudo docker run -d -p 8080:80 --name recargasapp-nginx recargasapp-nginx

            # Verificar que el contenedor esté corriendo
            if [ $(sudo docker ps -q -f name=recargasapp-nginx) ]; then
              echo "✅ Despliegue exitoso! La aplicación está corriendo en el puerto 8080"
            else
              echo "❌ Error: El contenedor no se pudo iniciar"
              exit 1
            fi

            # Limpiar imágenes no utilizadas
            sudo docker image prune -f
          '

      - name: Verify deployment
        run: |
          echo "🚀 Aplicación desplegada exitosamente!"
          echo "📍 Servidor: ${{ secrets.GCP_SERVER_HOST }}"
          echo "🌐 URL: http://${{ secrets.GCP_SERVER_HOST }}:8080"

      - name: Clean up
        run: |
          rm -f ~/.ssh/id_ed25519
          rm -f ~/.ssh/config
