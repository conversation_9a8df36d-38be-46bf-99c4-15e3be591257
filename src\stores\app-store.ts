import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { LocalStorage } from 'quasar';
import { Empresa } from '../components/comun/empresaModel';
import { Servidor } from '../components/comun/servidorModel';

export const useAppStore = defineStore('oApp', () => {
  const usuario = ref('');
  const codigo = ref(0);
  const APP_ADMIN = ref(0);
  const appCodigo = ref(0);
  const empresaCodigo = ref(0);
  const maxConsumidorFinal = ref(0);
  const empresasRegistradas = ref(0);
  const estaLogeado = ref(false);
  const url = ref(window.location.href);
  const darkMode = ref(false);
  const token = ref('');
  const login = ref('');
  // const appUrl = 'http://192.168.50.14:8080/apisage/webresources';
  const appUrl =
    'https://www.apromedfarmaloja-cloud.com:8181/apisage/webresources';
  const iso = ref('');
  const pos = ref('');
  const alm_codigo = ref(0);
  const version = '1.0.2';

  const IMAGE_PATH = ref(
    '/opt/control-asistencia/control_asistencia_api/src/public/fotos'
  );
  const servidores = ref<Servidor[]>([
    {
      id: 1,
      nombre: 'LoxaSoluciones 1',
      urlDesarrollo: 'http://192.168.50.14:8080/apisage/webresources/empresas',
      urlApiSSL:
        'https://www.loxasoluciones-cloud.com:8181/apisage/webresources/empresas',
      urlApi:
        'https://www.loxasoluciones-cloud.com:8181/apisage/webresources/empresas',
    },
    // {
    //   id: 2,
    //   nombre: 'PIÑAS INTERPROVINCIAL',
    //   urlDesarrollo: 'http://192.168.50.14:8080/apisage/webresources/empresas',
    //   urlApiSSL:
    //     'http://www.loxasoluciones-cloud1.com:8080/apisage/webresources/empresas',
    //   urlApi:
    //     'http://www.loxasoluciones-cloud1.com:8080/apisage/webresources/empresas',
    // },
    // {
    //   id: 3,
    //   nombre: 'CIFA INTERNACIONAL',
    //   urlDesarrollo: 'http://192.168.50.14:8080/apisage/webresources/empresas',
    //   urlApiSSL: 'http://181.39.106.50:8080/apisage/webresources/empresas',
    //   urlApi: 'http://181.39.106.50:8080/apisage/webresources/empresas',
    // },
  ]);
  const servidor = ref<Servidor>(servidores.value[0]);
  const apiEmpresas = ref(servidor.value.urlApiSSL);
  const empresa = ref<Empresa>({
    codigo_empresa: 0,
    ruc: '',
    index: 0,
    razon_social: '',
    nombre_comercial: '',
    url_local_api: '',
    url_publico_api: '',
  });
  const empresaCero = ref<Empresa>({
    codigo_empresa: 0,
    ruc: '',
    index: 0,
    razon_social: '',
    nombre_comercial: '',
    url_local_api: '',
    url_publico_api: '',
  });

  const API_URL = computed(() => {
    return appCodigo.value == 1
      ? empresa.value?.url_local_api
      : empresa.value?.url_publico_api;
  });

  const getHttpHeaders = computed(() => ({
    'Content-Type': 'application/json',
    token: token.value,
    usucodigo: codigo.value,
  }));

  const PATH = computed(() => `${API_URL.value}/static`);

  const getURLApi = computed(() => API_URL.value);

  const getCodigo = computed(() => codigo.value);

  const getUsuario = computed(() => usuario.value);

  const setDarkMode = (dark: boolean) => {
    darkMode.value = dark;
  };

  const iniciarSesion = (
    newToken: string,
    newCodigo: number,
    newAppCodigo: number,
    newUsuario: string,
    newLogin: string,
    new_AlmCodigo: number,
    newIso: string,
    newPos: string,
    newAPP_ADMIN: number
  ) => {
    estaLogeado.value = true;
    token.value = newToken;
    codigo.value = newCodigo;
    appCodigo.value = newAppCodigo;
    usuario.value = newUsuario;
    login.value = newLogin;
    alm_codigo.value = new_AlmCodigo;
    iso.value = newIso;
    pos.value = newPos;
    APP_ADMIN.value = newAPP_ADMIN;
    LocalStorage.set('session', {
      estaLogeado: estaLogeado.value,
      currentURL: url.value,
      token: token.value,
      usuario: usuario.value,
      codigo: codigo.value,
      appCodigo: appCodigo.value,
      login: login.value,
      darkMode: darkMode.value,
      alm_codigo: alm_codigo.value,
      iso: iso.value,
      pos: pos.value,
      // iso: '015912000100004',
      // pos: '10957975',
      APP_ADMIN: APP_ADMIN.value,
    });
  };

  const iniciarSesionConciliaciones = (
    newToken: string,
    newCodigo: number,
    newAppCodigo: number,
    newUsuario: string,
    newLogin: string
  ) => {
    estaLogeado.value = true;
    token.value = newToken;
    codigo.value = newCodigo;
    appCodigo.value = newAppCodigo;
    usuario.value = newUsuario;
    login.value = newLogin;
    APP_ADMIN.value = 1;

    LocalStorage.set('session', {
      estaLogeado: estaLogeado.value,
      currentURL: url.value,
      token: token.value,
      usuario: usuario.value,
      codigo: codigo.value,
      appCodigo: appCodigo.value,
      login: login.value,
      darkMode: darkMode.value,
      APP_ADMIN: APP_ADMIN.value,
    });
  };

  const cerrarSesion = () => {
    estaLogeado.value = false;
    token.value = '';
    usuario.value = '';
    codigo.value = 0;
    APP_ADMIN.value = 0;
    LocalStorage.set('session', {
      estaLogeado: estaLogeado.value,
      currentURL: url.value,
      token: token.value,
      usuario: usuario.value,
      codigo: codigo.value,
      appCodigo: appCodigo.value,
      login: '',
      darkMode: darkMode.value,
      alm_codigo: '',
      iso: '',
      pos: '',
      APP_ADMIN: 0,
    });
  };

  const actualizarDatos = (
    new_AlmCodigo: number,
    newIso: string,
    newPos: string
  ) => {
    alm_codigo.value = new_AlmCodigo;
    iso.value = newIso;
    pos.value = newPos;
  };

  const setDarkModeFromLocalStorage = (dark: boolean) => {
    const session = LocalStorage.getItem('session');
    if (session) {
      darkMode.value = dark;
    }
  };

  return {
    usuario,
    codigo,
    appCodigo,
    empresaCodigo,
    API_URL,
    APP_ADMIN,
    PATH,
    estaLogeado,
    url,
    token,
    login,
    iso,
    pos,
    appUrl,
    alm_codigo,
    getURLApi,
    getHttpHeaders,
    getCodigo,
    getUsuario,
    empresa,
    empresaCero,
    maxConsumidorFinal,
    empresasRegistradas,
    servidor,
    servidores,
    apiEmpresas,
    IMAGE_PATH,
    darkMode,
    version,
    setDarkMode,
    cerrarSesion,
    iniciarSesion,
    actualizarDatos,
    setDarkModeFromLocalStorage,
    iniciarSesionConciliaciones,
  };
});
