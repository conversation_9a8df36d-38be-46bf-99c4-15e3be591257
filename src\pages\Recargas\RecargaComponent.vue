<script setup lang="ts">
import axios from 'axios';
import { useQuasar } from 'quasar';
import { jwtDecode } from 'jwt-decode';
import { useAxios } from 'src/services/useAxios';
import { useAppStore } from 'src/stores/app-store';
import { computed, onMounted, ref, watch } from 'vue';
import { useMensajes } from 'src/services/useMensajes';
import { validarRUC } from 'src/services/useFacturacion';
import ImprimirPOSComponent from './ImprimirPOSComponent.vue';
import { cedRucRule, emailRule } from 'src/services/useReglas';
import { Loading, LocalStorage, QSpinnerFacebook } from 'quasar';
import { RespuestaTransaccion } from 'src/components/operadores';
import { deducirMensajeError, handleResponse } from 'src/utils/AppUtils';
import {
  Session,
  ObjectError,
  TokenDecoded,
  DatosFactura,
  CuentasObject,
  RespuestaFactura,
  TransaccionObject,
  RepuestaTransferencia,
} from 'src/components/models';
import {
  getRecarga,
  getOperator,
  formatMonto,
  enviarRecarga,
  obtenerCuentas,
  recargaExitosa,
  actualizarProveedor,
  obtenerAutorizacion,
  obtenerTransferencia,
  registrarTransferencia,
  obtenerMaxConsumidorFinal,
} from 'src/services/useRecargas';

const valor = defineModel<number>('valor', { required: true });
const nuevo = defineModel<boolean>('nuevo', { required: true });
const imagen = defineModel<string>('imagen', { required: true });
const comprar = defineModel<boolean>('comprar', { required: true });
const producto = defineModel<string>('producto', { required: true });
const proveedor = defineModel<string>('proveedor', { required: true });
const descripcion = defineModel<string>('descripcion', { required: true });

// Data
const $q = useQuasar();
const { mostrarError } = useMensajes();
const isPwd = ref(true);
const numero = ref('');
const lote = ref('');
const fecha = ref('');
const hora = ref('');
const facturar = ref(false);
const transferencia = ref(false);
const respuestaRecarga = ref('');
const autorizacionRecarga = ref('');
const cajero = ref('1234');
const clave = ref('1234');
const { get, post, put } = useAxios();
const appStore = useAppStore();
const secuencial = ref(0);
const recarga = ref(1);
const comprobante = ref<string>('');
const loadingTransferencia = ref(false);
const cuentas = ref<CuentasObject[]>([]);
const respuestaTransferencia = ref<RepuestaTransferencia[]>([]);
const cuenta = ref<CuentasObject>({
  bct_descri: 'BANCO DE LOJA 2903807463',
  bct_codigo: '21',
});

const alm_codigo = ref(0);
const iso = ref('');
const pos = ref('');

const opciones = [
  { label: 'Consumidor final', value: 0 },
  { label: 'Datos', value: 1, color: 'green' },
];
const opcionesPago = [
  { label: 'Efectivo', value: 1 },
  { label: 'Transferencia', value: 2, color: 'green' },
];
const eleccion = ref<number | null>(null);
const eleccionPago = ref<number | null>(null);
const facturado = ref(false);
const recargaHecha = ref(false);
const conDatos = ref(false);

const ruc = ref('');
const email = ref('');
const telefono = ref('');
const direccion = ref('');
const razonSocial = ref('');
const factura = ref('');
const imprimir = ref(false);
const cliente = ref(0);
const facturaSuccess = ref(true);

// Methods
const handleClick = async () => {
  if (eleccionPago.value === 2) {
    const response = await registrarTransferencia(
      parseInt(cuenta.value.bct_codigo),
      comprobante.value,
      secuencial.value
    );

    if (response.error == 'N') {
      transferencia.value = true;
      await generarFactura();
    }
  } else {
    await generarFactura();
  }
};

const isButtonDisabled = computed(() => {
  return (
    eleccion.value === null ||
    (!rucValido.value && eleccion.value === 1) ||
    razonSocial.value === '' ||
    direccion.value === '' ||
    (eleccion.value === 0 && valor.value > appStore.maxConsumidorFinal) ||
    (eleccionPago.value === 2 && comprobante.value === '')
  );
});

const registrarFactura = async () => {
  const response: RespuestaFactura = await put(
    '/registrar_factura',
    {},
    {
      codigo: secuencial.value,
      clp_codigo: cliente.value,
      autorizacion:
        autorizacionRecarga.value !== null ? autorizacionRecarga.value : '',
      ced_ruc: eleccion.value === 0 ? '9999999999999' : ruc.value,
      celular: telefono.value,
      clp_calles: direccion.value,
      clp_descri: razonSocial.value,
      email: email.value,
      usu_codigo: appStore.codigo,
      alm_codigo: appStore.alm_codigo,
      valor: valor.value,
      recarga: getRecarga(producto.value, proveedor.value),
      factura: factura.value,
    }
  );
  return response;
};

const generarFactura = async () => {
  try {
    Loading.show({
      spinner: QSpinnerFacebook,
      message: 'Procesando factura...',
    });

    const response: RespuestaFactura = await registrarFactura();

    if (response.error == 'N') {
      const token = appStore.token;
      const codigoFactura = response.objetos[0]?.codigo;
      const decodedToken = jwtDecode<TokenDecoded>(token);

      if (codigoFactura) {
        try {
          // Configuramos un timeout de 30 segundos
          const url = `${appStore.appUrl}/comun/transaccion/crear_factura_recarga?codigo=${codigoFactura}`;
          const headers = {
            login: appStore.login,
            clave: decodedToken.password,
            codigo_empresa: appStore.empresa.codigo_empresa,
            version_frontend: 'none',
          };

          const apiResponse = await axios.get(url, {
            headers,
            timeout: 30000, // 30 segundos en milisegundos
          });

          if (apiResponse.data.error === 'N') {
            factura.value = apiResponse.data.objetos[1];

            const response2: RespuestaFactura = await put(
              '/registrar_factura',
              {},
              {
                codigo: secuencial.value,
                clp_codigo: cliente.value,
                autorizacion:
                  autorizacionRecarga.value !== null
                    ? autorizacionRecarga.value
                    : '',
                ced_ruc: eleccion.value === 0 ? '9999999999999' : ruc.value,
                celular: telefono.value,
                clp_calles: direccion.value,
                clp_descri: razonSocial.value,
                email: email.value,
                usu_codigo: appStore.codigo,
                alm_codigo: appStore.alm_codigo,
                valor: valor.value,
                recarga: getRecarga(producto.value, proveedor.value),
                factura: factura.value,
              }
            );

            handleResponse(response2);

            if (response2.error == 'N') {
              imprimir.value = true;
              facturado.value = true;
            } else {
              facturaSuccess.value = false;
              mostrarError(
                'No se pudo generar la factura. ' + response2.mensaje,
                undefined
              );
              return;
            }
          } else {
            facturaSuccess.value = false;
            mostrarError(
              'No se pudo generar la factura. ' + apiResponse.data.mensaje,
              undefined
            );
            return;
          }
        } catch (apiError) {
          facturaSuccess.value = false;
          if (
            axios.isAxiosError(apiError) &&
            apiError.code === 'ECONNABORTED'
          ) {
            mostrarError(
              'No se pudo generar la factura. El servicio tardó demasiado en responder.',
              undefined
            );
          } else {
            mostrarError(
              'No se pudo generar la factura. El servicio no responde.',
              undefined
            );
          }
        }
      }
    } else {
      mostrarError('Error al registrar la factura en el sistema.', undefined);
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  } finally {
    Loading.hide();
  }
};

const checkRecarga = async (code: number) => {
  try {
    const response = await get('/check_recarga_exitosa', { codigo: code });

    if (response.error === 'S') {
      handleResponse(response);
    } else {
      if (response.objetos[0].recarga_exitosa) {
        recargaHecha.value = true;
        facturar.value = true;
        // generarFactura();
      } else {
        mostrarError('La recarga no ha sido exitosa.', 'center');
      }
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  }
};

const procesarRecarga = async (tran: TransaccionObject) => {
  try {
    Loading.show({
      spinner: QSpinnerFacebook,
      message: 'Procesando recarga...',
    });

    let response: RespuestaTransaccion | null = await enviarRecarga(tran);

    if (response?.datos) {
      if (response?.error === 'N') {
        respuestaRecarga.value = response.datos.codigoRespuesta;
        autorizacionRecarga.value = response.datos.numeroAutorizacion;

        if (respuestaRecarga.value === '00') {
          recargaHecha.value = true;
          facturar.value = true;
          await registrarFactura();
          // generarFactura();
          await recargaExitosa(secuencial.value);
          Loading.hide();
          return;
        } else {
          handleResponse(response);
        }
      }
    }

    if (response?.error === 'S') {
      mostrarError(response.mensaje, 'center');
      mostrarError(response.datos.mensajeRespuesta, 'center');
      return;
    }

    Loading.hide();
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  } finally {
    Loading.hide();
  }
};

const registrarRecarga = async () => {
  let nuevoProveedor = actualizarProveedor(producto.value, proveedor.value);

  try {
    const response = await post(
      '/registrar_recarga',
      {},
      JSON.parse(
        JSON.stringify({
          grupo: appStore.empresa.nombre_comercial,
          cadena: appStore.login,
          comercio: appStore.empresa.nombre_comercial,
          proveedor: getOperator(proveedor.value),
          producto: getRecarga(producto.value, proveedor.value),
          cajero: cajero.value,
          pvp: valor.value,
          referencia: '280000',
          pos: pos.value,
          iso: iso.value,
          // pos: '10957975',
          // iso: '015912000100004',
          telefono: numero.value,
          codigo_proveedor: nuevoProveedor,
          codigo_producto: producto.value,
        })
      )
    );

    if (response.error === 'N') {
      secuencial.value = response.objetos[0].codigo;
      lote.value = response.objetos[0].lote;
      fecha.value = response.objetos[0].fecha;
      hora.value = response.objetos[0].hora;

      const transaccion = {
        peticionRequerimiento: {
          tipoTransaccion: '02',
          codigoProceso: '280000',
          monto: formatMonto(valor.value),
          cajero: cajero.value,
          clave: clave.value,
          // tid: '10957975',
          // mid: '015912000100004',
          tid: pos.value,
          mid: iso.value,
          proveedor: nuevoProveedor,
          servicio: producto.value,
          cuenta: numero.value,
          autorizacion: obtenerAutorizacion(
            proveedor.value,
            producto.value,
            secuencial.value
          ),
          referencia: secuencial.value.toString().padStart(6, '0'),
          lote: lote.value,
          sbContrapartidaNombre: '',
          sbCedula: '',
          sbDireccion: '',
          sbTelefono: '',
          sbReferencia: '',
          sbReferenciaAdicional: '',
          sbCiudadCliente: '',
          sbCorreoDTV: '',
          modeloTerminal: '',
          fecha: fecha.value,
          hora: hora.value,
        },
      };

      procesarRecarga(transaccion);
    } else {
      handleResponse(response);
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  }
};

const resetearValores = () => {
  numero.value = '';
  comprar.value = false;
  if (producto.value === '02') {
    valor.value = 0;
  }
  facturar.value = false;
  recargaHecha.value = false;
  conDatos.value = false;
  imprimir.value = false;
  facturado.value = false;
  facturaSuccess.value = true;
  ruc.value = '';
  email.value = '';
  telefono.value = '';
  direccion.value = '';
  razonSocial.value = '';
  eleccion.value = 0;
  fecha.value = '';
  hora.value = '';
  recarga.value = 1;
  respuestaTransferencia.value = [];
  window.location.reload();
};

const resetearDatos = () => {
  ruc.value = '';
  eleccion.value = 0;
  email.value = '';
  telefono.value = '';
  direccion.value = '';
  razonSocial.value = '';
  imprimir.value = false;
  facturado.value = false;
  comprar.value = false;
};

const confirmarCompra = async () => {
  $q.dialog({
    title: 'Confirmar',
    message: `¿Esta seguro de realizar la recarga para el número ${numero.value}?`,
    cancel: {
      label: 'Cancelar', // Texto del botón de cancelar
      color: 'negative', // Cambia el color del botón si lo deseas
    },
    ok: {
      label: 'Confirmar', // Texto del botón de aceptar
      color: 'positive', // Cambia el color del botón si lo deseas
    },
    persistent: true,
  }).onOk(async () => {
    recarga.value = 0;
    await registrarRecarga();
  });
};

const handleCerrar = () => {
  if (recargaHecha.value && !facturado.value) {
    confirmarCerrar();
  } else {
    resetearValores();
  }
};

const handleTransferencia = async () => {
  loadingTransferencia.value = true;
  try {
    const timeout = setTimeout(() => {
      loadingTransferencia.value = false; // Desactivar el loading después de 4 segundos
    }, 4000);

    respuestaTransferencia.value = await obtenerTransferencia(
      comprobante.value
    );
    if (hayTransferencias.value) {
      await registrarTransferencia(
        parseInt(cuenta.value.bct_codigo),
        comprobante.value,
        secuencial.value
      );
    }
    clearTimeout(timeout);
    loadingTransferencia.value = false;
  } catch (error) {
    loadingTransferencia.value = false; // Desactivar el loading en caso de error
    mostrarError('Error al validar la transferencia.', undefined);
  }
};

const hayTransferencias = computed(
  () => respuestaTransferencia.value.length > 0
);

const confirmarCerrar = () => {
  $q.dialog({
    title: 'Confirmar',
    message: `
      <div>
        <p>¿Está seguro de cerrar esta ventana?</p>
        <p>La recarga no está <strong>facturada</strong>.</p>
        <p>Puede facturar la recarga más adelante en el módulo <strong>'Recargas sin facturar'</strong>. </p>
        <p> Sin embargo, es importante que defina correctamente los datos de la factura antes de cerrar la ventana, ya que una vez cerrada, no podrá modificar dichos datos.</p> </div>
      </div>
    `,
    html: true, // Esto permite que el mensaje sea interpretado como HTML
    cancel: {
      label: 'Cancelar', // Texto del botón de cancelar
      color: 'negative', // Cambia el color del botón si lo deseas
    },
    ok: {
      label: 'Confirmar', // Texto del botón de aceptar
      color: 'positive', // Cambia el color del botón si lo deseas
    },
    persistent: true,
  }).onOk(async () => {
    if (eleccionPago.value === 2) {
      await registrarTransferencia(
        parseInt(cuenta.value.bct_codigo),
        comprobante.value,
        secuencial.value
      );
      await registrarFactura();
      resetearValores();
    } else {
      await registrarFactura();
      resetearValores();
    }
  });
};

// Cargar cajero y clave desde localStorage cuando el diálogo se abra
watch(comprar, (newVal) => {
  if (newVal) {
    const savedCajero = localStorage.getItem('cajero');
    const savedClave = localStorage.getItem('clave');

    if (savedCajero) {
      cajero.value = savedCajero;
    }
    if (savedClave) {
      clave.value = savedClave;
    }
  }

  if (newVal === false) {
    resetearValores();
  }
});

// Watch para actualizar localStorage cuando cambien los valores de cajero o clave
watch([cajero, clave], ([newCajero, newClave]) => {
  localStorage.setItem('cajero', newCajero);
  localStorage.setItem('clave', newClave);
});

const obtenerDatos = async (ruc: string) => {
  const response: DatosFactura = await get('/datos_factura', {
    ced_ruc: ruc,
  });
  if (response.error == 'N') {
    cliente.value = response.objetos.clp_codigo;
    razonSocial.value = response.objetos.clp_descri;
    telefono.value = response.objetos.celular;
    direccion.value = response.objetos.clp_calles;
    email.value = response.objetos.email;
  }
};

watch(eleccion, (newValue) => {
  if (newValue === 1) {
    ruc.value = '';
    email.value = '';
    telefono.value = '';
    direccion.value = '';
    razonSocial.value = '';
  } else {
    ruc.value = '9999999999999';
    razonSocial.value = 'CONSUMIDOR FINAL';
    email.value = '';
    telefono.value = '';
    direccion.value = 'LOJA';
  }
});

watch(ruc, (newRuc) => {
  if (!comprar.value) return; // Solo ejecuta si `comprar` está activo
  if (newRuc.length === 13 || newRuc.length === 10) {
    obtenerDatos(newRuc as string);
  }
});

const rucValido = computed(() => validarRUC(ruc.value));

const isConfirmDisabled = computed(() => {
  return (
    recarga.value === 0 ||
    numero.value.length !== 10 ||
    !numero.value ||
    valor.value == 0 ||
    !cajero.value ||
    !clave.value ||
    eleccion.value === null ||
    (!rucValido.value && eleccion.value === 1) ||
    razonSocial.value === '' ||
    direccion.value === '' ||
    (eleccion.value === 0 && valor.value > appStore.maxConsumidorFinal)
  );
});

onMounted(async () => {
  const session: Session | null = LocalStorage.getItem('session');
  alm_codigo.value = session?.alm_codigo || 0;
  iso.value = session?.iso || '';
  pos.value = session?.pos || '';
  eleccionPago.value = 1;
  appStore.maxConsumidorFinal = await obtenerMaxConsumidorFinal();

  if (valor.value > appStore.maxConsumidorFinal) {
    eleccion.value = 1;
    conDatos.value = true;
  } else {
    eleccion.value = 0;
    conDatos.value = false;
  }
});

watch(
  [recargaHecha, eleccionPago],
  async ([newRecargaHecha, newEleccionPago]) => {
    if (newRecargaHecha && newEleccionPago === 2) {
      cuentas.value = await obtenerCuentas();
    }
  },
  { immediate: true }
);

const estado = computed(() => {
  if (valor.value > appStore.maxConsumidorFinal) {
    return { conDatos: true, eleccion: 1 };
  }
  return { conDatos: false, eleccion: 0 };
});

eleccion.value = estado.value.eleccion;
conDatos.value = estado.value.conDatos;

watch(valor, (newValue) => {
  if (newValue > appStore.maxConsumidorFinal) {
    eleccion.value = 1;
    conDatos.value = true;
  } else {
    eleccion.value = 0;
    conDatos.value = false;
  }
});

watch(eleccionPago, (newValue) => {
  if (newValue === 2) {
    eleccion.value = 1;
    conDatos.value = true;
  } else {
    conDatos.value = false;
  }
});
</script>

<template>
  <q-dialog v-model="comprar" persistent>
    <q-card class="my-card" flat bordered>
      <q-card-section class="row items-center q-pb-none">
        <div
          :class="[
            $q.screen.xs ? 'text-h6' : 'text-h5',
            appStore.darkMode ? 'text-info' : 'text-primary',
            'q-mt-sm q-mb-xs',
          ]"
          style="font-family: 'Oswald'"
        >
          {{ producto === '02' ? 'RECARGA DE SALDO' : 'RECARGA DE DATOS' }}
        </div>
        <div class="q-pl-sm q-pt-sm">
          <q-btn
            flat
            rounded
            :color="recargaHecha ? 'positive' : 'primary'"
            :icon="recargaHecha ? 'check' : 'help'"
            dense
            :disable="secuencial === 0"
            @click="checkRecarga(secuencial)"
          >
            <q-tooltip
              anchor="center right"
              self="center left"
              :offset="[10, 10]"
            >
              <strong class="text-caption">{{
                recargaHecha ? 'Recarga exitosa' : 'Verificar recarga'
              }}</strong>
            </q-tooltip>
          </q-btn>
        </div>
        <q-space />
        <q-btn icon="close" flat round dense @click="handleCerrar" />
      </q-card-section>

      <q-card-section horizontal>
        <q-card-section class="q-pt-xs">
          <div
            :class="
              $q.screen.xs
                ? 'text-subtitle2 q-mt-sm q-mb-xs'
                : 'text-h6 q-mt-sm q-mb-xs'
            "
          >
            <span v-if="producto === '02'"
              >RECARGA TIEMPO AIRE {{ getOperator(proveedor) }}</span
            >
            <span v-else
              >RECARGA PAQUETE PREPAGO {{ getOperator(proveedor) }} $
              {{ valor.toFixed(2) }}</span
            >
          </div>
          <div
            :class="
              appStore.darkMode
                ? 'text-caption text-white'
                : 'text-caption text-grey-8'
            "
            v-if="producto !== '02'"
          >
            {{ descripcion }}
          </div>
          <div>
            <q-badge
              outline
              color="primary"
              label="Recarga Solicitada"
              v-if="recarga == 0"
            >
              <q-tooltip
                transition-show="scale"
                transition-hide="scale"
                class="bg-amber text-caption text-black shadow-4"
                :offset="[10, 10]"
              >
                La recarga ha sido solicitada, si desea hacer otra recarga, por
                favor cierre esta ventana y vuelva a intentarlo.
              </q-tooltip>
            </q-badge>
          </div>
          <div>
            <q-badge
              outline
              color="secondary"
              label="Recarga Exitosa"
              v-if="recargaHecha"
            />
          </div>
          <div>
            <q-badge
              outline
              color="positive"
              label="Factura generada"
              v-if="facturado"
            />
          </div>
          <div>
            <q-badge
              outline
              color="orange"
              label="Factura no generada, puede generarla en 'Recargas sin facturar'"
              v-if="!facturaSuccess"
            />
          </div>
        </q-card-section>

        <q-card-section class="col-2 flex flex-center">
          <q-avatar rounded size="79px">
            <img :src="imagen" />
            <q-badge floating color="warning" v-if="nuevo">Nuevo</q-badge>
          </q-avatar>
        </q-card-section>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-py-xs" :class="$q.screen.xs ? 'q-py-xs' : ''">
        <div
          :class="
            appStore.darkMode
              ? 'text-caption text-white'
              : 'text-caption text-grey-8'
          "
        >
          <p class="gt-xs q-mb-none q-mt-xs">
            <strong>Transferencias:</strong> Todo pago con transferencia se debe
            hacer con datos de facturación.
          </p>
        </div>
      </q-card-section>

      <q-card-section class="q-py-xs" :class="$q.screen.xs ? 'q-py-xs' : ''">
        <div
          :class="
            appStore.darkMode
              ? 'text-caption text-white'
              : 'text-caption text-grey-8'
          "
        >
          <p class="gt-xs q-mb-none q-mt-xs">
            <strong>Nota:</strong> Antes de ingresar los datos del cliente para
            la facturación, seleccione una de las siguientes opciones:
          </p>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section horizontal>
        <!-- <q-card-section horizontal class="row justify-around"> -->
        <q-card-section>
          <div :class="$q.screen.xs ? 'q-pa-xs' : 'q-pa-xs'">
            <q-option-group
              :options="opciones"
              type="radio"
              v-model="eleccion"
              :disable="
                facturado || conDatos || valor > appStore.maxConsumidorFinal
              "
            />
          </div>
        </q-card-section>

        <q-separator vertical />

        <q-card-section>
          <div :class="$q.screen.xs ? 'q-pa-xs' : 'q-pa-xs'">
            <q-option-group
              :options="opcionesPago"
              type="radio"
              :disable="transferencia"
              v-model="eleccionPago"
            />
          </div>
        </q-card-section>
      </q-card-section>

      <q-separator v-if="recargaHecha && eleccionPago == 2" />

      <q-card-section v-if="recargaHecha && eleccionPago == 2">
        <p class="q-pb-xs q-mb-xs">
          <strong>Datos de la Transferencia: </strong>
        </p>
        <div style="width: 350px">
          <q-select
            dense
            outlined
            v-model="cuenta"
            :options="cuentas"
            option-value="bct_codigo"
            option-label="bct_descri"
            label="Cuenta de banco"
          />
        </div>

        <div class="row q-mt-md">
          <q-input
            filled
            square
            dense
            v-model="comprobante"
            label="Número de comprobante"
            hint="Ej: 60016844"
          />
          <q-btn
            uneleva
            no-caps
            class="q-mx-sm q-mb-md"
            :color="recargaHecha ? 'positive' : 'primary'"
            dense
            label="Validar"
            :disable="comprobante === '' || hayTransferencias"
            @click="handleTransferencia"
            v-if="cuenta.bct_codigo == '21'"
          >
            <q-tooltip
              anchor="center right"
              self="center left"
              :offset="[10, 10]"
            >
              <strong class="text-caption" v-if="!hayTransferencias">
                Verificar pago con transferencia
              </strong>
              <br v-if="!hayTransferencias" />
              <span class="text-caption" v-if="!hayTransferencias"
                >Solo disponible para Banco de loja</span
              >
              <span class="text-caption" v-if="hayTransferencias"
                >La transferencia ya se validó</span
              >
            </q-tooltip>
            <q-inner-loading :showing="loadingTransferencia">
              <q-spinner-facebook size="30px" color="primary" />
            </q-inner-loading>
          </q-btn>
        </div>

        <div class="q-pa-md" v-if="hayTransferencias">
          <q-badge
            outline
            color="secondary"
            label="Transferencia válida"
            class="q-mb-md"
          />
          <q-table
            :rows="respuestaTransferencia"
            row-key="name"
            flat
            bordered
            hide-bottom
          />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pt-md q-pb-xs q-px-xs">
        <div class="row justify-evenly">
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs"><strong>Número celular:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              :disable="recargaHecha"
              v-model="numero"
              dense
              :label="$q.screen.xs ? '# celular' : 'Ingrese el número celular'"
              :rules="[
                (val) => val.length === 10 || 'El número debe tener 10 dígitos',
              ]"
            />
          </div>
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs"><strong>Valor en USD:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model.number="valor"
              dense
              :disable="producto !== '02' || recargaHecha"
              prefix="$"
              type="number"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator inset />

      <q-card-section class="q-py-md q-px-xs">
        <div class="row justify-evenly">
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs"><strong>Cajero:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="cajero"
              dense
              :label="$q.screen.xs ? '# de cajero' : 'Ingrese el # de cajero'"
            />
          </div>
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs"><strong>Clave:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="clave"
              dense
              :type="isPwd ? 'password' : 'text'"
              :label="$q.screen.xs ? 'Contraseña' : 'Ingrese la clave'"
            >
              <template v-slot:append>
                <q-icon
                  :name="isPwd ? 'visibility_off' : 'visibility'"
                  class="cursor-pointer"
                  @click="isPwd = !isPwd"
                />
                <q-tooltip
                  class="bg-grey-6 text-caption"
                  anchor="bottom middle"
                  self="center middle"
                >
                  {{ isPwd ? 'Mostrar clave' : 'Ocultar clave' }}
                </q-tooltip>
              </template>
            </q-input>
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section class="q-pt-md">
        <div
          :class="
            appStore.darkMode
              ? 'text-caption text-white'
              : 'text-caption text-grey-8'
          "
        >
          <p class="gt-xs q-mb-none q-mt-xs">
            <strong>Nota:</strong> ¡Ingrese primero la cédula o el RUC!

            <span
              >Los datos marcados con <strong style="color: red">*</strong> son
              obligatorios para generar la factura con datos.</span
            >
          </p>
        </div>

        <div :class="$q.screen.xs ? 'column' : 'row justify-evenly'">
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs">
              <strong>Cédula o RUC:</strong> <span style="color: red">*</span>
            </p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              v-model="ruc"
              :disable="!eleccion"
              dense
              label="Ingrese la cédula o el RUC"
              :rules="cedRucRule"
            />
          </div>
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs">
              <strong>Razón Social:</strong> <span style="color: red">*</span>
            </p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="razonSocial"
              dense
              label="Nombre o Razón Social"
              :disable="!eleccion"
            />
          </div>
        </div>
        <div :class="$q.screen.xs ? 'column q-mt-md' : 'row justify-evenly'">
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs"><strong>Email:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              v-model="email"
              dense
              label="Email"
              :rules="emailRule"
              :disable="!eleccion"
            />
          </div>
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs"><strong>Teléfono:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="telefono"
              dense
              label="Número de teléfono o celular"
              :disable="!eleccion"
            />
          </div>
        </div>
        <div class="row justify-evenly">
          <div
            class="column col-11"
            :class="$q.screen.xs ? 'column col-12 q-mt-md' : 'column col-11'"
          >
            <p class="q-pb-xs q-mb-xs gt-xs">
              <strong>Dirección: </strong> <span style="color: red">*</span>
            </p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              v-model="direccion"
              dense
              label="Dirección"
              :disable="!eleccion"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-actions align="center">
        <q-btn
          color="positive"
          class="q-mt-md q-px-md"
          style="width: 100%"
          @click="confirmarCompra"
          :disable="isConfirmDisabled"
          v-if="!recargaHecha"
        >
          Confirmar Recarga
          <q-tooltip
            transition-show="scale"
            transition-hide="scale"
            class="bg-amber text-caption text-black shadow-4"
            :offset="[10, 10]"
            v-if="recarga == 0"
          >
            La recarga ha sido solicitada, si desea hacer otra recarga, por
            favor cierre esta ventana y vuelva a intentarlo.
          </q-tooltip>
        </q-btn>

        <q-btn
          color="positive"
          class="q-mt-md q-px-md"
          style="width: 100%"
          :disabled="isButtonDisabled"
          @click="handleClick"
          v-if="recargaHecha && !facturado"
        >
          Generar Factura

          <q-tooltip
            transition-show="scale"
            transition-hide="scale"
            class="bg-amber text-caption text-black shadow-4"
            :offset="[10, 10]"
            v-if="isButtonDisabled"
          >
            Completa todos los campos requeridos para continuar.
          </q-tooltip>
        </q-btn>
      </q-card-actions>
      <ImprimirPOSComponent
        class="col-6"
        v-model:ruc="ruc"
        v-model:valor="valor"
        v-model:fecha="fecha"
        v-model:numero="numero"
        v-model:factura="factura"
        v-model:telefono="telefono"
        v-model:producto="producto"
        v-model:imprimir="imprimir"
        v-model:proveedor="proveedor"
        v-model:direccion="direccion"
        v-model:razonSocial="razonSocial"
        @cerrarFactura="resetearDatos"
      />
    </q-card>
  </q-dialog>
</template>
