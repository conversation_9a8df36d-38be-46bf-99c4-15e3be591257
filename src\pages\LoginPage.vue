<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref, onMounted, watch } from 'vue';
import { Session } from '../components/models';
import { useAxios } from '../services/useAxios';
import { useAppStore } from '../stores/app-store';
import { useMensajes } from '../services/useMensajes';
import type { Empresa } from '../components/comun/empresaModel';
import type { ObjectError, Opcion } from '../components/models';
import { LocalStorage, Loading, QSpinnerFacebook } from 'quasar';
import EmpresasComponent from '../components/EmpresasComponent.vue';
import { deducirMensajeError, handleResponse } from '../utils/AppUtils';

const router = useRouter();
const appStore = useAppStore();
const editorRegistro = ref(false);
const opcion = ref<Opcion | null>(null);
const db_empresas = 'db_empresas_controlasistencias';
const empresa = ref<Empresa>({
  codigo_empresa: 0,
  ruc: '',
  index: 0,
  razon_social: '',
  nombre_comercial: '',
  url_local_api: '',
  url_publico_api: '',
});
const empresas = ref<Empresa[]>([]);
const options = ref([
  { label: 'Conexión Local', valor: 1 },
  { label: 'Conexión por Internet', valor: 2 },
]);
const url = ref(appStore.url);
const { get, put } = useAxios();
const { mostrarMensaje } = useMensajes();
const mostrarVentana = ref(false);
const correoElectronico = ref('');
const ruc = ref('');
const id = ref('');
const clave = ref('');
const isPwd = ref(true);
const newUrl = ref(url.value.slice(url.value.indexOf('#') + 1));
const token = ref('');
const emailRule: ((v: string) => string | boolean)[] = [
  (v: string) => !!v || 'El correo electrónico es obligatorio',
  (v: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) ||
    'Formato de correo electrónico inválido',
];

const codigo = ref(0);
const usuario = ref('');
const alm_codigo = ref(0);
const iso = ref('');
const pos = ref('');
const APP_ADMIN = ref(1);

const cargarEmpresas = () => {
  const ls_empresas = LocalStorage.getItem<Array<Empresa>>(db_empresas);
  if (ls_empresas) {
    ls_empresas.forEach((it, index) => {
      it.index = index;
      empresas.value.push(it);
      if (index === 0) {
        empresa.value = it;
        appStore.empresa = empresa.value;
      }
    });
    appStore.empresasRegistradas = ls_empresas.length;
  }
  LocalStorage.set(db_empresas, empresas.value);
};

onMounted(() => {
  cargarEmpresas();

  // Restaura la opción seleccionada del LocalStorage si existe
  const opcionGuardada = LocalStorage.getItem<number>('opcion_seleccionada');

  if (opcionGuardada !== null) {
    appStore.appCodigo = opcionGuardada;
  }

  if (appStore.empresa) {
    empresa.value = appStore.empresa;
  }
  const session: Session | null = LocalStorage.getItem('session');
  id.value = session?.login || '';
  usuario.value = session?.usuario || '';
  codigo.value = session?.codigo || 0;
  token.value = session?.token || '';
  alm_codigo.value = session?.alm_codigo || 0;
  iso.value = session?.iso || '';
  pos.value = session?.pos || '';
  APP_ADMIN.value = session?.APP_ADMIN || 0;

  if (session?.appCodigo == 1) {
    opcion.value = options.value[0];
  } else {
    opcion.value = options.value[1];
  }

  if (session?.estaLogeado == true) {
    appStore.iniciarSesion(
      token.value,
      codigo.value,
      appStore.appCodigo,
      usuario.value,
      id.value,
      alm_codigo.value,
      iso.value,
      pos.value,
      APP_ADMIN.value
    );
    router.push(newUrl.value);
  }
});

const logearse = async () => {
  if (id.value.trim().length === 0) {
    mostrarMensaje(
      'Error',
      'Es necesario ingresar su nombre de usuario para continuar'
    );
    return;
  }
  if (clave.value.trim().length === 0) {
    mostrarMensaje('Error', 'Es obligatorio ingresar su clave de acceso');
    return;
  }
  Loading.show({
    spinner: QSpinnerFacebook,
    message: 'Verificando acceso...',
  });
  const respuesta = await get('/validar_usuario', {
    id: id.value,
    clave: clave.value,
    sys: 0,
  });

  if (respuesta.error === 'S') {
    handleResponse(respuesta);
    Loading.hide();
    return;
  }

  let objetos;
  let objetos2;

  if (respuesta.error === 'N') {
    objetos = respuesta.objetos[0];

    const respuesta2 = await get('/recargas_codigos', {
      usu_codigo: objetos.codigo,
    });

    if (respuesta2.error === 'S') {
      handleResponse(respuesta2);
      Loading.hide();
      return;
    }

    if (respuesta2.error === 'N' && respuesta2.mensaje === 'Acceso permitido') {
      APP_ADMIN.value = 1;
      appStore.iniciarSesionConciliaciones(
        respuesta.token,
        objetos.codigo,
        appStore.appCodigo,
        objetos.usu_nomape,
        objetos.usu_login
      );
      router.push('/conciliaciones');
    }

    if (respuesta2.error === 'N' && respuesta2.objetos.length > 0) {
      objetos2 = respuesta2.objetos[0];
    } else {
      if (appStore.APP_ADMIN !== 1) {
        mostrarMensaje('Error', 'No tiene asignado un ISO ni POS para operar');
      }
      Loading.hide();
      return;
    }
  }

  Loading.hide();
  if (respuesta.error === 'S') {
    mostrarMensaje('Error', respuesta.mensaje);
    return;
  }
  appStore.iniciarSesion(
    respuesta.token,
    objetos.codigo,
    appStore.appCodigo,
    objetos.usu_nomape,
    objetos.usu_login,
    objetos2.alm_codigo.toString(),
    objetos2.iso,
    objetos2.pos,
    APP_ADMIN.value
  );
  router.push('/');
};

const recuperarContraseña = () => {
  mostrarVentana.value = true;
};

const enviarCorreoRecuperacion = async () => {
  try {
    const response = await put(
      '/resetear_clave_acceso',
      {},
      JSON.parse(
        JSON.stringify({
          email: correoElectronico.value,
          ruc: ruc.value,
        })
      )
    );

    // Handle the response accordingly
    if (response.error === 'N') {
      mostrarMensaje(
        'Su nueva clave ha sido enviada a su correo electrónico. Por favor, revise la carpeta de spam si no la encuentra en la bandeja de entrada.',
        response.mensaje
      );
      handleCerrarDialogo();
      return;
    }

    if (response.error === 'S') {
      mostrarMensaje('Error', response.mensaje);
      return;
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  }
};

const handleCerrarDialogo = () => {
  ruc.value = '';
  mostrarVentana.value = false;
  correoElectronico.value = '';
};

watch(opcion, () => {
  if (opcion.value !== null) {
    appStore.appCodigo = opcion.value.valor;
    LocalStorage.set('opcion_seleccionada', opcion.value.valor);
  }
});

watch(empresa, async () => {
  appStore.empresa = empresa.value;
});
</script>

<template>
  <q-page
    :class="
      appStore.darkMode
        ? 'flex flex-center dark-bg-image'
        : 'flex flex-center bg-image'
    "
    v-if="!appStore.estaLogeado"
  >
    <q-dialog v-model="mostrarVentana" persistent>
      <q-card style="width: 250px">
        <div class="row bg-blue-8 justify-center q-pa-xs">
          <span
            class="text-h6 text-center text-white"
            style="font-family: 'Bebas Neue'"
            >Cambiar clave de acceso</span
          >
        </div>
        <q-card-section>
          <q-input
            v-model="ruc"
            debounce="750"
            :color="appStore.darkMode ? 'info' : 'primary'"
            label="Login (Nombre de usuario)"
            dense
          />
          <q-input
            v-model="correoElectronico"
            :color="appStore.darkMode ? 'info' : 'primary'"
            debounce="1000"
            label="Email"
            dense
            :rules="emailRule"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            color="primary"
            label="Enviar"
            @click="enviarCorreoRecuperacion()"
          />
          <q-btn flat label="Cerrar" @click="handleCerrarDialogo" />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <EmpresasComponent
      v-model:editorRegistro="editorRegistro"
      v-model:empresa="empresa"
      v-model:empresas="empresas"
    />

    <q-card class="shadow-8" style="width: 300px; height: 380px">
      <div class="row bg-blue-8 justify-center q-pa-xs">
        <span
          class="text-h6 text-center text-white"
          style="font-family: 'Bebas Neue'"
          >RECARGAS APP V{{ appStore.version }}</span
        >
      </div>
      <div class="row">
        <div class="row justify-center q-pa-sm">
          <div style="min-width: 240px">
            <q-select
              outlined
              dense
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="appStore.empresa"
              :options="empresas"
              label="Seleccione la empresa"
              option-label="nombre_comercial"
            />
          </div>
          <q-btn
            flat
            round
            icon="more_vert"
            @click="editorRegistro = true"
            :color="appStore.darkMode ? 'info' : 'primary'"
          />
        </div>
        <div class="column col-xs-12 q-pa-sm">
          <q-select
            outlined
            dense
            v-model="opcion"
            :options="options"
            :color="appStore.darkMode ? 'info' : 'primary'"
            label="Modo de Conexión"
            option-label="label"
          />
        </div>
        <div class="column col-xs-12 q-pa-sm">
          <q-input v-model="id" type="text" label="Usuario" dense />
        </div>
        <div class="column col-xs-12 q-pa-sm">
          <q-input
            v-model="clave"
            dense
            :type="isPwd ? 'password' : 'text'"
            label="Clave de acceso"
            @keyup.enter="logearse()"
          >
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
              />
            </template>
          </q-input>
        </div>
      </div>
      <q-separator dark />
      <div class="row">
        <div class="column col-xs-12 q-pa-sm">
          <q-btn
            class="full-width text-white"
            style="height: 40px"
            color="primary"
            label="Ingresar"
            @click="logearse()"
            :disable="
              id.trim().length === 0 ||
              clave.trim().length === 0 ||
              appStore.empresa?.nombre_comercial.trim().length === 0
            "
          />
        </div>
      </div>

      <div class="row">
        <div class="column col-xs-12 q-pa-sm">
          <a
            class="full-width text-secondary q-link"
            style="
              display: block;
              height: 40px;
              line-height: 40px;
              text-align: center;
            "
            @click="recuperarContraseña()"
          >
            <span
              :class="
                appStore.darkMode
                  ? 'text-bold text-info'
                  : 'text-bold text-blue-grey'
              "
              >¿OLVIDASTE TU CONTRASEÑA?</span
            >
          </a>
        </div>
      </div>
    </q-card>
  </q-page>
</template>

<style scoped>
@import '../css/login.page.scss';
</style>
