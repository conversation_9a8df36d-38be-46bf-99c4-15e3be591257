<script setup lang="ts">
import { ref } from 'vue';
import RecargaComponent from './RecargaComponent.vue';

// Definimos los datos que se van a usar en el componente
const proveedor = ref('');
const producto = ref('02'); // Establecido como 02 por defecto
const valor = ref(0);
const comprar = ref(false);
const descripcion = ref('');
const nuevo = ref(false);
const imagen = ref('');

// Manejador de clic para actualizar los datos según el proveedor
const setProveedorData = (proveedorCode: string, imagenPath: string) => {
  proveedor.value = proveedorCode;
  comprar.value = true;
  imagen.value = imagenPath;
};

// Importa las imágenes
import claroImage from '../../assets/claro.png';
import movistarImage from '../../assets/movistar.webp';
import tuentiImage from '../../assets/tuenti.png';
import cntImage from '../../assets/cnt.png';
import maxiplusImage from '../../assets/maxiplus.png';
import akimovilImage from '../../assets/akimovil.png';

// Datos de cada proveedor
const proveedores = [
  { code: '05', image: claroImage },
  { code: '26', image: movistarImage },
  { code: '27', image: tuentiImage },
  { code: '06', image: cntImage },
  { code: '16', image: maxiplusImage },
  { code: '17', image: akimovilImage },
];
</script>

<template>
  <div class="row wrap justify-evenly q-my-md">
    <q-btn
      unelevated
      @click="setProveedorData(proveedores[0].code, proveedores[0].image)"
    >
      <q-avatar rounded size="79px">
        <img :src="proveedores[0].image" />
      </q-avatar>
    </q-btn>
    <q-btn
      unelevated
      @click="setProveedorData(proveedores[1].code, proveedores[1].image)"
    >
      <q-avatar rounded size="79px">
        <img :src="proveedores[1].image" />
      </q-avatar>
    </q-btn>
    <q-btn
      unelevated
      @click="setProveedorData(proveedores[2].code, proveedores[2].image)"
    >
      <q-avatar rounded size="79px">
        <img :src="proveedores[2].image" />
      </q-avatar>
    </q-btn>
    <q-btn
      unelevated
      @click="setProveedorData(proveedores[3].code, proveedores[3].image)"
    >
      <q-avatar rounded size="79px">
        <img :src="proveedores[3].image" />
      </q-avatar>
    </q-btn>
    <q-btn
      unelevated
      @click="setProveedorData(proveedores[4].code, proveedores[4].image)"
    >
      <q-avatar rounded size="79px">
        <img :src="proveedores[4].image" />
      </q-avatar>
    </q-btn>
    <q-btn
      unelevated
      @click="setProveedorData(proveedores[5].code, proveedores[5].image)"
    >
      <q-avatar rounded size="79px">
        <img :src="proveedores[5].image" />
      </q-avatar>
    </q-btn>
  </div>

  <q-separator inset />

  <RecargaComponent
    v-model:proveedor="proveedor"
    v-model:producto="producto"
    v-model:valor="valor"
    v-model:comprar="comprar"
    v-model:descripcion="descripcion"
    v-model:nuevo="nuevo"
    v-model:imagen="imagen"
  />
</template>
