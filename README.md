# Recargas App (recargasapp)

Aplicación de recargas de Loxasoluciones

## Run docker file

docker build -t recargasapp-nginx .

## Run the image

docker run -d -p 8080:80 recargasapp-nginx

## Install the dependencies

```bash
yarn
# or
npm install
```

### Start the app in development mode (hot-code reloading, error reporting, etc.)

```bash
quasar dev
```

### Lint the files

```bash
yarn lint
# or
npm run lint
```

### Format the files

```bash
yarn format
# or
npm run format
```

### Build the app for production

```bash
quasar build
```

### Customize the configuration

See [Configuring quasar.config.js](https://v2.quasar.dev/quasar-cli-vite/quasar-config-js).
