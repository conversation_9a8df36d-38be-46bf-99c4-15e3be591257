<script setup lang="ts">
import { useAppStore } from '../stores/app-store';
import { SucursalesObject } from '../components/models';

const date = defineModel<string>('date', { required: true });
const desde = defineModel<string>('desde', { required: true });
const hasta = defineModel<string>('hasta', { required: true });
const filter = defineModel<string>('filter', { required: true });
const blueModel = defineModel<boolean>('blueModel', { required: true });
const sucursal = defineModel<SucursalesObject>('sucursal', { required: true });
const sucursales = defineModel<SucursalesObject[]>('sucursales', {
  required: true,
});
const appStore = useAppStore();

const emits = defineEmits([
  'procesarConciliaciones',
  'procesarConciliacionesFechas',
]);

const obtenerConciliaciones = (dateEvent: string, posEvent: string) => {
  emits('procesarConciliaciones', dateEvent, posEvent);
};

const obtenerConciliacionesFechas = (
  desdeEvent: string,
  hastaEvent: string,
  posEvent: string
) => {
  emits('procesarConciliacionesFechas', desdeEvent, hastaEvent, posEvent);
};
</script>

<template>
  <div class="column q-pa-md">
    <div>
      <q-toggle
        :label="
          blueModel ? `Registros por rango de fechas` : `Registros por día`
        "
        v-model="blueModel"
      />
    </div>
    <div class="row justify-left">
      <div class="gt-xs q-ma-sm">
        <q-input
          outlined
          :color="appStore.darkMode ? 'info' : 'primary'"
          input-class="text-right"
          dense
          debounce="350"
          borderless
          v-model="filter"
          placeholder="Buscar..."
          hint="Escriba para buscar en la tabla"
        >
          <template v-slot:append>
            <q-icon v-if="filter === ''" name="search" />
            <q-icon
              v-else
              name="clear"
              class="cursor-pointer"
              @click="filter = ''"
            />
          </template>
        </q-input>
      </div>

      <div class="row q-ma-sm">
        <q-input
          filled
          dense
          :color="appStore.darkMode ? 'info' : 'primary'"
          v-model="date"
          mask="date"
          :rules="['date']"
          label="Seleccione el día"
          v-if="!blueModel"
        >
          <template v-slot:append>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy
                cover
                transition-show="scale"
                transition-hide="scale"
              >
                <q-date v-model="date">
                  <div class="row items-center justify-end">
                    <q-btn v-close-popup label="Cerrar" color="primary" flat />
                  </div>
                </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
        <div class="row q-pa-none" v-if="blueModel">
          <div class="q-pt-none q-pl-xs" style="max-width: 200px">
            <q-input
              debounce="350"
              v-model="desde"
              mask="date"
              :rules="['date']"
              outlined
              dense
              options-dense
              label="Fecha desde:"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="desde">
                      <div class="row items-center justify-end">
                        <q-btn
                          v-close-popup
                          label="Cerrar"
                          color="primary"
                          flat
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>

          <div class="q-pt-none q-pl-md" style="max-width: 200px">
            <q-input
              debounce="350"
              v-model="hasta"
              mask="date"
              outlined
              dense
              options-dense
              label="Fecha hasta:"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date v-model="hasta">
                      <div class="row items-center justify-end">
                        <q-btn
                          v-close-popup
                          label="Cerrar"
                          color="primary"
                          flat
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
        </div>

        <div class="q-ml-md" style="width: 200px">
          <q-select
            dense
            outlined
            v-model="sucursal"
            :options="sucursales"
            option-value="pos"
            option-label="alm_nomcom"
            label="Sucursal"
          />
        </div>

        <div class="xs q-ml-md">
          <q-btn
            push
            color="primary"
            round
            icon="search"
            @click="obtenerConciliaciones(date, sucursal.pos)"
            :disable="!date"
          />
        </div>
      </div>

      <div class="xs q-ma-sm">
        <q-input
          outlined
          :color="appStore.darkMode ? 'info' : 'primary'"
          input-class="text-right"
          dense
          debounce="350"
          borderless
          v-model="filter"
          placeholder="Buscar..."
          hint="Escriba para buscar en la tabla"
        >
          <template v-slot:append>
            <q-icon v-if="filter === ''" name="search" />
            <q-icon
              v-else
              name="clear"
              class="cursor-pointer"
              @click="filter = ''"
            />
          </template>
        </q-input>
      </div>

      <div class="q-ma-sm gt-xs" style="margin-top: 11px" v-if="!blueModel">
        <q-btn
          unelevated
          color="primary"
          label="Buscar registro"
          icon="search"
          @click="obtenerConciliaciones(date, sucursal.pos)"
          :disable="!date"
        />
      </div>

      <div class="q-ma-sm gt-xs" style="margin-top: 11px" v-if="blueModel">
        <q-btn
          unelevated
          color="primary"
          label="Buscar registros"
          icon="search"
          @click="obtenerConciliacionesFechas(desde, hasta, sucursal.pos)"
          :disable="!desde || !hasta"
        />
      </div>
    </div>
  </div>
</template>
