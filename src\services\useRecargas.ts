import { useAxios } from 'src/services/useAxios';
import { useAppStore } from '../stores/app-store';
import { useMensajes } from '../services/useMensajes';
import {
  deducirMensajeError,
  handleResponse,
  mostrarNotificacion,
} from '../utils/AppUtils';
import {
  ObjectError,
  SucursalesObject,
  RespuestaPvPTotal,
  TransaccionObject,
} from '../components/models';

const { get, post, put } = useAxios();

const appStore = useAppStore();

const { mostrarError } = useMensajes();

export const emailRule: ((v: string) => string | boolean)[] = [
  (v: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) ||
    'Formato de correo electrónico inválido',
];

export const getOperator = (code: string): string => {
  switch (code) {
    case '05':
      return 'CLARO';
    case '01':
      return 'MOVISTAR';
    case '26':
      return 'MOVISTAR';
    case '83':
      return 'TUENTI';
    case '27':
      return 'TUENTI';
    case '06':
      return 'CNT';
    case '17':
      return 'AKIMOVIL';
    case '16':
      return 'MAXIPLUS';
    default:
      return 'Operador no encontrado';
  }
};

export const formatMonto = (value: number): string => {
  const montoStr = (value * 100).toFixed(0);
  return montoStr.padStart(12, '0');
};

export const actualizarProveedor = (prod: string, prov: string): string => {
  let nuevoProveedor = prov; // Estado temporal

  if (prod === '02') {
    if (nuevoProveedor === '26') {
      nuevoProveedor = '01';
    } else if (nuevoProveedor === '27') {
      nuevoProveedor = '83';
    }
  }

  return nuevoProveedor;
};

export const obtenerAutorizacion = (
  proveedor: string,
  producto: string,
  secuencial: number
) => {
  const operador = getOperator(proveedor);

  if (operador === 'CNT') {
    return '';
  }

  if (producto === '02' && (operador === 'MOVISTAR' || operador === 'TUENTI')) {
    return '';
  }

  return secuencial.toString().padStart(6, '0');
};

export const enviarRecarga = async (tran: TransaccionObject) => {
  try {
    const respuesta = await post(
      '/recargar',
      {},
      JSON.parse(JSON.stringify(tran))
    );
    return respuesta;
  } catch (error) {
    mostrarNotificacion({
      color: 'red-5',
      icon: 'warning',
      message: '¡No se recibió respuesta del servidor!',
    });
  }
};

export const contieneTimeout = (response: { mensaje: string }): boolean => {
  return response.mensaje.toLowerCase().includes('timeout');
};

export const recargaExitosa = async (code: number) => {
  try {
    const response = await put(
      '/registrar_recarga_exitosa',
      {},
      JSON.parse(JSON.stringify({ codigo: code }))
    );
    handleResponse(response);
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  }
};

export const obtenerMaxConsumidorFinal = async () => {
  try {
    const response = await get('/max_consumidor_final', {});
    if (response.error === 'S') {
      handleResponse(response);
    } else {
      if (response.objetos.length === 0) {
        return 0;
      } else {
        return response.objetos[0].max_consumidor_final;
      }
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const obtenerSucursales = async () => {
  try {
    const response = await get('/listar_farmacias', {});
    if (response.error === 'S') {
      handleResponse(response);
    } else {
      return response.objetos;
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const obtenerTransferencia = async (cod: string) => {
  try {
    const response = await get('/obtener_transferencia_registrada', {
      codigo: cod,
    });
    if (response.error === 'S') {
      handleResponse(response);
    } else {
      return response.objetos;
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const obtenerCuentas = async () => {
  try {
    const response = await get('/listar_cuentas', {});
    if (response.error === 'S') {
      handleResponse(response);
    } else {
      return response.objetos;
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const registrarTransferencia = async (
  codigo: number,
  numero: string,
  secuencia: number
) => {
  try {
    const response = await put(
      '/registrar_transferencia',
      {},
      {
        bct_codigo: codigo,
        numero_transaccion: numero,
        codigo: secuencia,
        fpago: 2,
      }
    );
    return response;
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  }
};

export const recargasSinFacturar = async (iso_codigo: string) => {
  try {
    const response = await get('/recargas_sin_facturar', {
      iso: iso_codigo,
      login: appStore.login,
    });
    if (response.error === 'N') {
      return response.objetos;
    } else {
      return [];
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const recargasFacturadas = async (
  num: number,
  regs: number,
  desde: string,
  hasta: string
) => {
  try {
    const response = await get('/recargas_facturadas', {
      numero_de_pagina: num,
      registros_por_pagina: regs,
      fecha_desde: desde,
      fecha_hasta: hasta,
      pos: appStore.pos,
    });
    if (response.error === 'N') {
      return response.objetos;
    } else {
      return [];
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const numeroRecargasFacturadas = async (
  desde: string,
  hasta: string
) => {
  try {
    const response = await get('/numero_recargas_facturadas', {
      fecha_desde: desde,
      fecha_hasta: hasta,
      pos: appStore.pos,
    });
    if (response.error === 'N') {
      return response.objetos[0].count;
    } else {
      return [];
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
    return [];
  }
};

export const getRecarga = (producto: string, proveedor: string): string => {
  if (producto === '02') {
    return `RECARGA DE SALDO ${getOperator(proveedor)}`;
  } else if (producto === '15') {
    return `RECARGA PAQUETE DE DATOS ${getOperator(proveedor)}`;
  } else if (proveedor == '05' && producto === '19') {
    return 'GIGAS LIBRES POR 2 HORAS CLARO';
  } else if (proveedor == '05' && producto === '27') {
    return 'SUSCRIPCIÓN CLARO MÚSICA';
  } else if (proveedor == '26' && producto === '22') {
    return 'BONO VOZ MOVISTAR';
  } else if (proveedor == '26' && producto === '24') {
    return 'BONO DATOS MOVISTAR';
  } else if (proveedor == '27' && producto === '22') {
    return 'COMODÍN VOZ TUENTI';
  } else if (proveedor == '06' && producto === '18') {
    return 'CNT 1 GB Google + YouTube';
  } else if (proveedor == '06' && producto === '28') {
    return 'CNT 1 GB Instagram';
  } else if (proveedor == '06' && producto === '33') {
    return 'CNT 1 GB Netflix';
  } else if (proveedor == '06' && producto === '29') {
    return 'CNT 1 GB Spotify';
  } else if (proveedor == '06' && producto === '30') {
    return 'CNT 1 GB TikTok';
  } else if (proveedor == '06' && producto === '31') {
    return 'CNT 1 GB Waze';
  } else if (proveedor == '06' && producto === '32') {
    return 'CNT 5 GB Zoom + Teams + Webex';
  } else if (proveedor == '06' && producto === '24') {
    return 'CNT Paquete de datos';
  } else {
    return producto;
  }
};

const getCurrentDate = (): string => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = String(now.getMonth() + 1).padStart(2, '0'); // Los meses comienzan en 0
  const year = now.getFullYear();

  return `${day}-${month}-${year}`;
};

export const obtenerNombreSucursal = (
  pos: string,
  branches: SucursalesObject[]
) => {
  const sucursal = branches.find((f) => f.pos === pos);
  return sucursal ? sucursal.alm_nomcom : pos;
};

export const obtenerPvPtotal = async (
  date: string,
  suc: string,
  api: number
) => {
  let request = '';
  if (api == 1) {
    request = '/pvp_total';
  } else if (api == 2) {
    request = '/pvp_total_no_facturado';
  } else {
    return 0;
  }
  const respuesta: RespuestaPvPTotal = await get(request, {
    fecha: date,
    pos: suc,
  });
  if (respuesta.error === 'S') {
    mostrarError(respuesta.mensaje, undefined);
    return 0;
  } else {
    return respuesta?.objetos[0].total_pvp ?? 0;
  }
};

export const obtenerPvPtotalFechas = async (
  from: string,
  to: string,
  suc: string,
  api: number
) => {
  let requestFechas = '';
  if (api == 1) {
    requestFechas = '/pvp_total_fechas';
  } else if (api == 2) {
    requestFechas = '/pvp_total_fechas_no_facturado';
  } else {
    return 0;
  }

  const respuesta: RespuestaPvPTotal = await get(requestFechas, {
    fecha_desde: from,
    fecha_hasta: to,
    pos: suc,
  });
  if (respuesta.error === 'S') {
    mostrarError(respuesta.mensaje, undefined);
    return 0;
  } else {
    return respuesta?.objetos[0].total_pvp ?? 0;
  }
};

import jsPDF from 'jspdf';

export const convertHtmlPdf = (
  ruc: string,
  valor: number,
  fecha: string,
  numero: string,
  factura: string,
  telefono: string,
  producto: string,
  proveedor: string,
  direccion: string,
  razonSocial: string
) => {
  // Crear una nueva instancia de jsPDF
  const doc = new jsPDF({
    unit: 'pt', // Cambiar unidades a puntos
    // format: 'a4',
    format: [224, 400],
  });

  // Configurar el ancho del recibo (224) ancho del contenido 2.5 pulg. (180)
  const receiptWidth = 220;

  // Configurar fuente y tamaño
  doc.setFont('Helvetica', 'normal');
  doc.setFontSize(10);

  const texto = 'FARMACIAS AMERICANAS';

  const centrarTexto = (text: string, receiptWidth: number) => {
    const textWidth = doc.getTextWidth(text);
    const xCentered = (receiptWidth - textWidth) / 2;
    return xCentered;
  };

  doc.setFont('Helvetica', 'bold');
  doc.text(texto, centrarTexto(texto, receiptWidth), 30);

  doc.setFontSize(7);
  const texto2 = 'DISTRIBUIDORA APROMED SAS';

  doc.setFont('Helvetica', 'bold');
  doc.text('DISTRIBUIDORA APROMED SAS', centrarTexto(texto2, receiptWidth), 50);

  doc.setFontSize(7.25);
  doc.setFont('Helvetica', 'bold');
  doc.text('RUC:', 20, 65);

  doc.setFont('Helvetica', 'normal');
  doc.text(appStore.empresa.ruc, 40, 65);

  doc.setFont('Helvetica', 'bold');
  doc.text('Teléfono:', 20, 75);

  doc.setFont('Helvetica', 'normal');
  doc.text('072713158 2575-662', 55, 75);

  const address = 'Av. Eugenio Espejo 200-31 y Adolfo Valarezo esquina';

  const maxWidth = receiptWidth - 80; // Ajusta según el margen inicial (48 en tu caso)

  const addressLines1 = doc.splitTextToSize(address, maxWidth);

  doc.setFont('Helvetica', 'bold');
  doc.text('Dirección:', 20, 85);

  doc.setFont('Helvetica', 'normal');
  doc.text(addressLines1, 60, 85);

  doc.setFont('Helvetica', 'bold');
  doc.text('OBLIGADO A LLEVAR CONTABILIDAD:', 20, 105);

  doc.setFont('Helvetica', 'normal');
  doc.text('SÍ', 170, 105);

  doc.setFont('Helvetica', 'bold');
  doc.text('Factura: ', 20, 120);
  doc.text(factura, 60, 120);

  doc.text('Fecha: ', 20, 130);

  doc.setFont('Helvetica', 'normal');
  doc.text(fecha, 60, 130);

  doc.setFont('Helvetica', 'bold');
  doc.text('RUC/CI: ', 20, 140);

  doc.setFont('Helvetica', 'normal');
  doc.text(ruc, 60, 140);

  doc.setFont('Helvetica', 'bold');
  doc.text('Nombre: ', 20, 150);

  doc.setFont('Helvetica', 'normal');
  const razonSocialLines = doc.splitTextToSize(razonSocial, maxWidth);
  doc.text(razonSocialLines, 60, 150);

  doc.setFont('Helvetica', 'bold');
  doc.text('Teléfono: ', 20, 167.5);

  doc.setFont('Helvetica', 'normal');
  doc.text(telefono, 60, 167.5);

  doc.setFont('Helvetica', 'bold');
  doc.text('Dirección: ', 20, 177.5);

  // Dividir el texto en líneas ajustadas al ancho máximo
  const addressLines2 = doc.splitTextToSize(direccion, maxWidth);

  doc.setFont('Helvetica', 'normal');
  doc.text(addressLines2, 60, 177.5);

  doc.setLineWidth(0.1);
  doc.setDrawColor(0, 0, 0);

  // doc.setLineDash([2.5]);
  doc.line(20, 190, 200, 190);

  const recarga: string = getRecarga(producto, proveedor);

  const maxWidth2 = receiptWidth - 40;

  const addressLines3 = doc.splitTextToSize(recarga, maxWidth2);

  doc.setFont('Helvetica', 'normal');
  doc.text(addressLines3, 20, 200);

  doc.setFont('Helvetica', 'bold');
  doc.text('Número: ', 20, 220);

  doc.setFont('Helvetica', 'normal');
  doc.text(numero, 60, 220);

  doc.setLineWidth(0.1);
  doc.setDrawColor(0, 0, 0);

  // doc.setLineDash([2.5]);
  doc.line(20, 225, 200, 225);

  doc.setFont('Helvetica', 'bold');
  doc.text('SUBTOTAL: ', 105, 240);

  doc.setFont('Helvetica', 'normal');
  doc.text((valor / 1.15).toFixed(2).toString(), 155, 240);

  doc.setFont('Helvetica', 'bold');
  doc.text('IVA 15 %: ', 105, 250);

  doc.setFont('Helvetica', 'normal');
  doc.text(((valor * 15) / 115).toFixed(2).toString(), 155, 250);

  doc.setFontSize(9);
  doc.setFont('Helvetica', 'bold');
  doc.text('TOTAL: ', 105, 261);

  doc.text(valor.toString(), 155, 261);

  doc.setFontSize(7);
  doc.setFont('Helvetica', 'bold');
  doc.text('Impreso: ', 105, 272);

  doc.setFont('Helvetica', 'normal');
  doc.text(getCurrentDate(), 155, 272);

  doc.setFont('Helvetica', 'bold');
  doc.text('Login: ', 105, 282);

  doc.setFont('Helvetica', 'normal');
  doc.text(appStore.login, 155, 282);

  // doc.output('dataurlnewwindow');
  doc.autoPrint({ variant: 'non-conform' });

  // Abrir PDF en una nueva ventana
  const pdfUrl = doc.output('bloburl');
  window.open(pdfUrl, '_blank');
};
