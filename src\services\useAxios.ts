import { axios } from '../boot/axios';
import { useAppStore } from '../stores/app-store';
import { deducirMensajeError } from '../utils/AppUtils';
import type { ObjectError } from '../components/models';

export function useAxios() {
  const appStore = useAppStore();
  const timeout = 1000 * 100;

  const get = async (url: string, params: object) => {
    try {
      const response = await axios({
        method: 'GET',
        url: `${appStore.getURLApi}${url}`,
        params,
        headers: appStore.getHttpHeaders,
        timeout: timeout,
      });
      return response.data;
    } catch (error) {
      return {
        error: 'S',
        mensaje: deducirMensajeError(error as ObjectError),
        objetos: [],
      };
    }
  };

  const post = async (url: string, params: object, jsonData: object) => {
    try {
      const response = await axios({
        method: 'POST',
        url: `${appStore.getURLApi}${url}`,
        params,
        data: jsonData,
        headers: {
          ...appStore.getHttpHeaders,
          'Content-Type': 'application/json', // Set the Content-Type header to specify JSON data
        },
        timeout: timeout,
      });
      return response.data;
    } catch (error) {
      return {
        error: 'S',
        mensaje: deducirMensajeError(error as ObjectError),
        objetos: [],
      };
    }
  };

  const put = async (url: string, params: object, jsonData: object) => {
    try {
      const response = await axios({
        method: 'PUT',
        url: `${appStore.getURLApi}${url}`,
        params,
        data: jsonData,
        headers: {
          ...appStore.getHttpHeaders,
          'Content-Type': 'application/json', // Set the Content-Type header to specify JSON data
        },
        timeout: timeout,
      });
      return response.data;
    } catch (error) {
      return {
        error: 'S',
        mensaje: deducirMensajeError(error as ObjectError),
        objetos: [],
      };
    }
  };

  return {
    get,
    post,
    put,
  };
}
