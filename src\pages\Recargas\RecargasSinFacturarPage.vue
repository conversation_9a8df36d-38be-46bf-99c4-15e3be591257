<script setup lang="ts">
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import { useAxios } from 'src/services/useAxios';
import { useAppStore } from '../../stores/app-store';
import { computed, onMounted, ref, watch } from 'vue';
import { columnasRNF } from '../../components/columns';
import { useMensajes } from '../../services/useMensajes';
import { validarRUC } from 'src/services/useFacturacion';
import { cedRucRule, emailRule } from 'src/services/useReglas';
import { Loading, LocalStorage, QSpinnerFacebook } from 'quasar';
import { deducirMensajeError, handleResponse } from '../../utils/AppUtils';
import { getRecarga, obtenerTransferencia } from 'src/services/useRecargas';
import {
  obtenerCuentas,
  recargasSinFacturar,
  registrarTransferencia,
} from '../../services/useRecargas';
import {
  Session,
  ObjectError,
  TokenDecoded,
  DatosFactura,
  CuentasObject,
  FilasRecargas,
  DetalleFactura,
  RespuestaFactura,
  RepuestaTransferencia,
} from '../../components/models';

// Data
const proveedor = ref('');
const cliente = ref(0);
const { mostrarError } = useMensajes();
const respuestaTransferencia = ref<RepuestaTransferencia[]>([]);
const loadingTransferencia = ref(false);
const producto = ref('02'); // Establecido como 02 por defecto
const valor = ref(0);
const factura = ref('');
const iso = ref('');
const ruc = ref('');
const razonSocial = ref('');
const email = ref('');
const telefono = ref('');
const direccion = ref('');
const consumidorFinal = ref(false);
const fpago = ref(0);
const cuentas = ref<CuentasObject[]>([]);
const comprobante = ref<string>('');
const cuenta = ref<CuentasObject>({
  bct_descri: 'BANCO DE LOJA 2903807463',
  bct_codigo: '21',
});

const { get, put } = useAxios();

const filter = ref('');
const filas = ref<FilasRecargas[]>([]);
const columnas = columnasRNF;
const appStore = useAppStore();
const visibleColumns = [
  'proveedor',
  'pos',
  'fecha',
  'hora',
  'pvp',
  'telefono',
  'codigo_producto',
];

const pagination = ref({
  sortBy: 'desc',
  descending: false,
  page: 1,
  rowsPerPage: 0,
});

const imprimir = ref(false);
const facturado = ref(false);
const expandedRow = ref<number | null>(null);

const toggleExpand = (
  rowId: number,
  detalleFactura: DetalleFactura,
  pvp: number,
  pago: number,
  transaccion: string
) => {
  fpago.value = pago;
  comprobante.value = transaccion;
  respuestaTransferencia.value = [];
  expandedRow.value = expandedRow.value === rowId ? null : rowId;
  if (detalleFactura !== null) {
    ruc.value = detalleFactura.ced_ruc;
    razonSocial.value = detalleFactura.clp_descri;
    email.value = detalleFactura.email;
    telefono.value = detalleFactura.celular;
    direccion.value = detalleFactura.clp_calles;
  } else if (detalleFactura === null && pvp < 7) {
    ruc.value = '9999999999999';
    razonSocial.value = 'CONSUMIDOR FINAL';
    email.value = '';
    telefono.value = '';
    direccion.value = 'LOJA';
  } else {
    ruc.value = '';
    razonSocial.value = '';
    email.value = '';
    telefono.value = '';
    direccion.value = '';
  }
};

const rucValido = computed(() => validarRUC(ruc.value));
const hayTransferencias = computed(
  () => respuestaTransferencia.value.length > 0
);

// Methods
const handleTransferencia = async () => {
  loadingTransferencia.value = true;

  try {
    const timeout = setTimeout(() => {
      loadingTransferencia.value = false;
    }, 4000);

    respuestaTransferencia.value = await obtenerTransferencia(
      comprobante.value
    );

    // Si la respuesta llega antes de 4 segundos, cancelar el timeout
    clearTimeout(timeout);
    loadingTransferencia.value = false;
  } catch (error) {
    loadingTransferencia.value = false;
    mostrarError('Error al validar la transferencia.', undefined);
  }
};

const facturarRecarga = async (
  fil: FilasRecargas,
  ruc: string,
  telefono: string,
  direccion: string,
  razonSocial: string,
  email: string
) => {
  if (fil.fpago === 2) {
    await registrarTransferencia(
      parseInt(cuenta.value.bct_codigo),
      comprobante.value,
      fil.codigo
    );
  }
  // Validación para evitar facturar a consumidor final con pago por transferencia
  if (fil.fpago == 2 && (ruc === '9999999999' || ruc === '9999999999999')) {
    mostrarError(
      'No se puede generar factura a consumidor final cuando el pago es con transferencia.',
      undefined
    );
    return; // Detener la ejecución de la función
  }
  try {
    Loading.show({
      spinner: QSpinnerFacebook,
      message: 'Procesando factura...',
    });

    const response: RespuestaFactura = await put(
      '/registrar_factura',
      {},
      {
        codigo: fil.codigo,
        clp_codigo: fil.codigo,
        autorizacion: fil.autorizacion !== null ? fil.autorizacion : '',
        ced_ruc: ruc,
        celular: telefono,
        clp_calles: direccion,
        clp_descri: razonSocial,
        email: email,
        usu_codigo: appStore.codigo,
        alm_codigo: appStore.alm_codigo,
        valor: fil.pvp,
        recarga: getRecarga(
          fil.codigo_producto.toString(),
          fil.codigo_proveedor.toString()
        ),
        factura: '',
      }
    );

    if (response.error == 'N') {
      const token = appStore.token;
      const decodedToken = jwtDecode<TokenDecoded>(token);
      const codigoFactura = response.objetos[0]?.codigo;

      if (codigoFactura) {
        try {
          // Configuramos un timeout de 30 segundos
          const url = `${appStore.appUrl}/comun/transaccion/crear_factura_recarga?codigo=${codigoFactura}`;
          const headers = {
            login: appStore.login,
            clave: decodedToken.password,
            // login: 'dfcarrera',
            // clave: '585150',
            codigo_empresa: appStore.empresa.codigo_empresa,
            version_frontend: 'none',
          };

          const apiResponse = await axios.get(url, {
            headers,
            timeout: 30000, // 30 segundos en milisegundos
          });

          if (apiResponse.data.error === 'N') {
            factura.value = apiResponse.data.objetos[1];

            const response2: RespuestaFactura = await put(
              '/registrar_factura',
              {},
              {
                codigo: fil.codigo,
                clp_codigo: fil.codigo,
                autorizacion: fil.autorizacion !== null ? fil.autorizacion : '',
                ced_ruc: ruc,
                celular: telefono,
                clp_calles: direccion,
                clp_descri: razonSocial,
                email: email,
                usu_codigo: appStore.codigo,
                alm_codigo: appStore.alm_codigo,
                valor: valor.value,
                recarga: getRecarga(producto.value, proveedor.value),
                factura: factura.value,
              }
            );

            handleResponse(response2);

            if (response2.error == 'N') {
              imprimir.value = true;
              facturado.value = true;
              filas.value = await recargasSinFacturar(iso.value);
              imprimir.value = false;
              facturado.value = false;
            } else {
              mostrarError(
                'No se pudo generar la factura. ' + response2.mensaje,
                undefined
              );
              return;
            }
          } else {
            mostrarError(
              'No se pudo generar la factura. ' + apiResponse.data.mensaje,
              undefined
            );
            return;
          }
        } catch (apiError) {
          if (
            axios.isAxiosError(apiError) &&
            apiError.code === 'ECONNABORTED'
          ) {
            mostrarError(
              'No se pudo generar la factura. El servicio tardó demasiado en responder.',
              undefined
            );
          } else {
            mostrarError(
              'No se pudo generar la factura. El servicio no responde.',
              undefined
            );
          }
        }
      }
    } else {
      mostrarError('Error al registrar la factura en el sistema.', undefined);
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  } finally {
    Loading.hide();
  }
};

watch(facturado, async (newVal) => {
  if (!newVal) {
    filas.value = await recargasSinFacturar(iso.value);
  }
});

watch(consumidorFinal, (newValue) => {
  if (newValue === true) {
    ruc.value = '9999999999999';
    razonSocial.value = 'CONSUMIDOR FINAL';
    email.value = '';
    telefono.value = 'XXXXXXXXXX';
    direccion.value = 'LOJA';
  } else {
    ruc.value = '';
    razonSocial.value = '';
    email.value = '';
    telefono.value = '';
    direccion.value = '';
  }
});

onMounted(async () => {
  const session: Session | null = LocalStorage.getItem('session');
  iso.value = session?.iso || '';
  filas.value = await recargasSinFacturar(iso.value);
  cuentas.value = await obtenerCuentas();
});

const obtenerDatos = async (ruc: string) => {
  const response: DatosFactura = await get('/datos_factura', {
    ced_ruc: ruc,
  });
  if (response.error == 'N') {
    cliente.value = response.objetos.clp_codigo;
    razonSocial.value = response.objetos.clp_descri;
    telefono.value = response.objetos.celular;
    direccion.value = response.objetos.clp_calles;
    email.value = response.objetos.email;
  }
};

watch(ruc, (newRuc) => {
  if (newRuc.length === 13 || newRuc.length === 10) {
    obtenerDatos(newRuc as string);
  }
});
</script>

<template>
  <div class="q-pt-sm">
    <h4
      class="row text-uppercase justify-center content-center q-my-sm q-pb-md"
      style="font-family: 'Bebas Neue'"
    >
      <div class="q-pt-sm gt-xs">RECARGAS SIN FACTURAR</div>
      <div class="q-pt-sm xs">Recargas sin facturar</div>
    </h4>
  </div>
  <div>
    <q-table
      class="text-h6 text-grey-8 justify-center"
      flat
      bordered
      no-data-label="Datos no disponibles"
      hide-no-data
      :rows="filas"
      :columns="columnas"
      :filter="filter"
      row-key="codigo"
      :visible-columns="visibleColumns"
      v-model:pagination="pagination"
      hide-pagination
    >
      <template v-slot:top-left>
        <p
          :class="
            appStore.darkMode
              ? 'text-info text-h5 q-pt-md'
              : 'text-primary text-h5 q-pt-md'
          "
          style="font-family: 'Bebas Neue'"
        >
          RECARGAS EXITOSAS
        </p>
      </template>

      <template v-slot:top-right>
        <q-input
          outlined
          input-class="text-right"
          dense
          debounce="350"
          borderless
          :color="appStore.darkMode ? 'info' : 'primary'"
          v-model="filter"
          placeholder="Buscar..."
        >
          <template v-slot:append>
            <q-icon v-if="filter === ''" name="search" />
            <q-icon
              v-else
              name="clear"
              class="cursor-pointer"
              @click="filter = ''"
            />
          </template>
        </q-input>
      </template>

      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th auto-width />
          <q-th
            :style="
              appStore.darkMode
                ? 'text-align: center; font-weight: bold; color: #68afed'
                : 'text-align: center; font-weight: bold; color: #1976d2'
            "
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td auto-width>
            <q-btn
              size="sm"
              color="primary"
              round
              dense
              @click="
                toggleExpand(
                  props.row.codigo,
                  props.row.detalle_factura,
                  props.row.pvp,
                  props.row.fpago,
                  props.row.numero_transaccion
                );
                props.expand = !props.expand;
              "
              :icon="props.expand ? 'remove' : 'add'"
            />
          </q-td>
          <q-td v-for="col in props.cols" :key="col.name" :props="props">
            {{ col.value }}
          </q-td>
        </q-tr>

        <q-tr v-show="expandedRow === props.row.codigo" :props="props">
          <q-td colspan="100%">
            <div style="max-width: 500px">
              <q-badge
                outline
                color="primary"
                label="Pago con transferencia"
                v-if="props.row.fpago == 2"
              >
                <q-tooltip
                  transition-show="scale"
                  transition-hide="scale"
                  class="bg-amber text-caption text-black shadow-4"
                  :offset="[10, 10]"
                >
                  Todo pago con transferencia se debe hacer con datos de
                  facturación.
                </q-tooltip>
              </q-badge>
              <q-toggle
                v-model="consumidorFinal"
                checked-icon="check"
                color="red"
                label="Consumidor Final"
                unchecked-icon="clear"
                :disable="
                  props.row.pvp > appStore.maxConsumidorFinal ||
                  props.row.fpago == 2
                "
              />

              <div v-if="fpago == 2">
                <div class="text-subtitle2">Datos de la Transferencia</div>
                <div style="width: 350px">
                  <q-select
                    dense
                    outlined
                    v-model="cuenta"
                    :options="cuentas"
                    option-value="bct_codigo"
                    option-label="bct_descri"
                    label="Cuenta de banco"
                  />
                </div>

                <div class="row q-mt-md">
                  <q-input
                    filled
                    square
                    dense
                    v-model="comprobante"
                    label="Número de comprobante"
                    hint="Ej: 60016844"
                  />
                  <q-btn
                    uneleva
                    no-caps
                    class="q-mx-sm q-mb-md"
                    color="positive"
                    dense
                    label="Validar"
                    :disable="comprobante === '' || hayTransferencias"
                    @click="handleTransferencia"
                    v-if="cuenta.bct_codigo == '21'"
                  >
                    <q-tooltip
                      anchor="center right"
                      self="center left"
                      :offset="[10, 10]"
                    >
                      <strong class="text-caption" v-if="!hayTransferencias">
                        Verificar pago con transferencia
                      </strong>
                      <br v-if="!hayTransferencias" />
                      <span class="text-caption" v-if="!hayTransferencias"
                        >Solo disponible para Banco de loja</span
                      >
                      <span class="text-caption" v-if="hayTransferencias"
                        >La transferencia ya se validó</span
                      >
                    </q-tooltip>

                    <q-inner-loading :showing="loadingTransferencia">
                      <q-spinner-facebook size="30px" color="primary" />
                    </q-inner-loading>
                  </q-btn>
                </div>
              </div>

              <div class="q-pa-md" v-if="hayTransferencias">
                <q-badge
                  outline
                  color="secondary"
                  label="Transferencia válida"
                  class="q-mb-md"
                />
                <q-table
                  :rows="respuestaTransferencia"
                  row-key="name"
                  flat
                  bordered
                  hide-bottom
                />
              </div>

              <div :class="$q.screen.xs ? 'column' : 'row justify-evenly'">
                <div class="column col-5">
                  <p class="q-pb-xs q-mb-xs gt-xs">
                    <strong>Cédula o RUC:</strong>
                    <span style="color: red">*</span>
                  </p>
                  <q-input
                    outlined
                    :color="appStore.darkMode ? 'info' : 'primary'"
                    debounce="1000"
                    v-model="ruc"
                    dense
                    label="Ingrese la cédula o el RUC"
                    :rules="cedRucRule"
                  />
                </div>
                <div class="column col-5">
                  <p class="q-pb-xs q-mb-xs gt-xs">
                    <strong>Razón Social:</strong>
                    <span style="color: red">*</span>
                  </p>
                  <q-input
                    outlined
                    :color="appStore.darkMode ? 'info' : 'primary'"
                    v-model="razonSocial"
                    dense
                    label="Nombre o Razón Social"
                  />
                </div>
              </div>
              <div
                :class="$q.screen.xs ? 'column q-mt-md' : 'row justify-evenly'"
              >
                <div class="column col-5">
                  <p class="q-pb-xs q-mb-xs gt-xs"><strong>Email:</strong></p>
                  <q-input
                    outlined
                    :color="appStore.darkMode ? 'info' : 'primary'"
                    debounce="1000"
                    v-model="email"
                    dense
                    label="Email"
                    :rules="emailRule"
                  />
                </div>
                <div class="column col-5">
                  <p class="q-pb-xs q-mb-xs gt-xs">
                    <strong>Teléfono:</strong>
                  </p>
                  <q-input
                    outlined
                    :color="appStore.darkMode ? 'info' : 'primary'"
                    v-model="telefono"
                    dense
                    label="Número de teléfono o celular"
                  />
                </div>
              </div>
              <div class="row justify-evenly">
                <div
                  class="column col-11"
                  :class="
                    $q.screen.xs ? 'column col-12 q-mt-md' : 'column col-11'
                  "
                >
                  <p class="q-pb-xs q-mb-xs gt-xs">
                    <strong>Dirección: </strong>
                    <span style="color: red">*</span>
                  </p>
                  <q-input
                    outlined
                    :color="appStore.darkMode ? 'info' : 'primary'"
                    debounce="1000"
                    v-model="direccion"
                    dense
                    label="Dirección"
                  />
                </div>
              </div>

              <q-btn
                color="positive"
                class="q-mt-md q-px-md"
                style="width: 100%"
                :disabled="
                  (!rucValido && valor > appStore.maxConsumidorFinal) ||
                  razonSocial === '' ||
                  direccion === '' ||
                  (comprobante == '' && fpago == 2)
                "
                @click="
                  facturarRecarga(
                    props.row,
                    ruc,
                    telefono,
                    direccion,
                    razonSocial,
                    email
                  )
                "
                v-if="!facturado"
              >
                Generar Factura

                <q-tooltip
                  transition-show="scale"
                  transition-hide="scale"
                  class="bg-amber text-caption text-black shadow-4"
                  :offset="[10, 10]"
                  v-if="valor > appStore.maxConsumidorFinal"
                >
                  {{
                    `El monto máximo para facturar a Consumidor Final es de $${appStore.maxConsumidorFinal} dólares`
                  }}
                </q-tooltip>
              </q-btn>
            </div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>
