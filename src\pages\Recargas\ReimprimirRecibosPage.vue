<script setup lang="ts">
import { useAppStore } from 'src/stores/app-store';
import { columnasRNF } from 'src/components/columns';
import { computed, onMounted, ref, watch } from 'vue';
import { convertHtmlPdf } from 'src/services/useRecargas';
import { getCurrentDate } from 'src/services/useWorkDays';
import { FilasRecargasFacturadas } from 'src/components/models';
import {
  recargasFacturadas,
  numeroRecargasFacturadas,
} from 'src/services/useRecargas';

// Data
const date = ref('');
const factura = ref('');
const proveedor = ref('');
const producto = ref('02'); // Establecido como 02 por defecto
const valor = ref(0);
const facturar = ref(false);
const comprar = ref(false);
const numero = ref('');
const fecha = ref('');
const secuencial = ref(0);
const ruc = ref('');
const telefono = ref('');
const direccion = ref('');
const razonSocial = ref('');

const page = ref(1);
const desde = ref('');
const hasta = ref('');
const numFilas = ref(0);

const filter = ref('');
const filas = ref<FilasRecargasFacturadas[]>([]);
const columnas = columnasRNF;
const appStore = useAppStore();
const visibleColumns = [
  'detalle_factura',
  'proveedor',
  'cadena',
  'pos',
  'fecha',
  'hora',
  'pvp',
  'telefono',
  'codigo_producto',
];

const pagination = ref({
  page: page.value,
  rowsPerPage: 50,
  rowsNumber: numFilas.value,
});

// Methods
const imprimirRecibo = (fil: FilasRecargasFacturadas) => {
  valor.value = fil.pvp;
  numero.value = fil.telefono;
  fecha.value = fil.fecha;
  facturar.value = true;
  proveedor.value = fil.codigo_proveedor.toString().padStart(2, '0');
  producto.value = fil.codigo_producto.toString().padStart(2, '0');
  secuencial.value = fil.codigo;
  comprar.value = true;
  factura.value = fil.detalle_factura.factura;
  ruc.value = fil.detalle_factura.ced_ruc;
  telefono.value = fil.detalle_factura.celular;
  direccion.value = fil.detalle_factura.clp_calles;
  razonSocial.value = fil.detalle_factura.clp_descri;

  convertHtmlPdf(
    ruc.value,
    valor.value,
    fecha.value,
    numero.value,
    factura.value,
    telefono.value,
    producto.value,
    proveedor.value,
    direccion.value,
    razonSocial.value
  );
  resetearDatos();
};

const resetearDatos = () => {
  valor.value = 0;
  numero.value = '';
  fecha.value = '';
  facturar.value = false;
  proveedor.value = '';
  producto.value = '02';
};

const pagesNumber = computed(() => {
  return Math.ceil(numFilas.value / pagination.value.rowsPerPage);
});

watch(page, async () => {
  filas.value = await recargasFacturadas(
    page.value,
    pagination.value.rowsPerPage,
    desde.value,
    hasta.value
  );
});

// Actualizar filas cuando se cierre el componente FacturarComponent
watch(facturar, async (newVal) => {
  if (!newVal) {
    // Cuando facturar cambia a false (diálogo cerrado)
    filas.value = await recargasFacturadas(
      page.value,
      pagination.value.rowsPerPage,
      desde.value,
      hasta.value
    );
  }
});

const onClick = async () => {
  filas.value = await recargasFacturadas(
    page.value,
    pagination.value.rowsPerPage,
    desde.value,
    hasta.value
  );
  numFilas.value = await numeroRecargasFacturadas(desde.value, hasta.value);
};

onMounted(async () => {
  date.value = getCurrentDate();
  desde.value = date.value;
  hasta.value = date.value;
  filas.value = await recargasFacturadas(
    page.value,
    pagination.value.rowsPerPage,
    desde.value,
    hasta.value
  );
  numFilas.value = await numeroRecargasFacturadas(desde.value, hasta.value);
});

const customFilter = (rows: FilasRecargasFacturadas[], searchQuery: string) => {
  if (!searchQuery) return rows; // Si no hay texto de búsqueda, retorna todas las filas

  const lowerSearchQuery = searchQuery.toLowerCase();

  return rows.filter((row) => {
    const detalleFactura = row.detalle_factura;
    // Asegúrate de que detalleFactura esté presente y que los campos que buscas estén definidos
    const clpDescriValido = detalleFactura?.clp_descri
      .toLowerCase()
      .includes(lowerSearchQuery);
    const telefonoValido = row.telefono
      ?.toLowerCase()
      .includes(lowerSearchQuery);
    const proveedorValido = row.proveedor
      ?.toLowerCase()
      .includes(lowerSearchQuery);

    const fechaValida = row.fecha?.toLowerCase().includes(lowerSearchQuery);
    const pvpValido = row.pvp
      ?.toString()
      .toLowerCase()
      .includes(lowerSearchQuery);

    return (
      clpDescriValido ||
      proveedorValido ||
      telefonoValido ||
      fechaValida ||
      pvpValido
    );
  });
};

const filteredRows = computed(() => {
  return customFilter(filas.value, filter.value);
});
</script>

<template>
  <div class="q-pt-sm">
    <h4
      class="row text-uppercase justify-center content-center q-my-sm q-pb-md"
      style="font-family: 'Bebas Neue'"
    >
      <div class="q-pt-sm">REIMPRESIÓN DE RECIBOS</div>
    </h4>
  </div>
  <div>
    <q-table
      class="text-h6 text-grey-8 justify-center"
      flat
      bordered
      no-data-label="Datos no disponibles"
      hide-no-data
      :rows="filteredRows"
      :columns="columnas"
      :filter="filter"
      row-key="codigo"
      :visible-columns="visibleColumns"
      v-model:pagination="pagination"
      hide-pagination
    >
      <template v-slot:top-left>
        <p
          :class="
            appStore.darkMode
              ? 'text-info text-h5 q-pt-md'
              : 'text-primary text-h5 q-pt-md'
          "
          style="font-family: 'Bebas Neue'"
        >
          RECARGAS FACTURADAS
        </p>
      </template>

      <template v-slot:top-right>
        <div class="row justify-center">
          <div class="q-ma-sm" style="max-width: 130px">
            <q-input
              debounce="350"
              v-model="desde"
              mask="date"
              outlined
              dense
              options-dense
              label="Fecha desde:"
              :color="appStore.darkMode ? 'info' : 'primary'"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date
                      v-model="desde"
                      minimal
                      :color="appStore.darkMode ? 'info' : 'primary'"
                    >
                      <div class="row items-center justify-end">
                        <q-btn
                          v-close-popup
                          label="Cerrar"
                          :color="appStore.darkMode ? 'info' : 'primary'"
                          flat
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>

          <div class="q-ma-sm" style="max-width: 130px">
            <q-input
              debounce="350"
              v-model="hasta"
              mask="date"
              outlined
              dense
              options-dense
              label="Fecha hasta:"
              :color="appStore.darkMode ? 'info' : 'primary'"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy
                    cover
                    transition-show="scale"
                    transition-hide="scale"
                  >
                    <q-date
                      v-model="hasta"
                      minimal
                      :color="appStore.darkMode ? 'info' : 'primary'"
                    >
                      <div class="row items-center justify-end">
                        <q-btn
                          v-close-popup
                          label="Cerrar"
                          :color="appStore.darkMode ? 'info' : 'primary'"
                          flat
                        />
                      </div>
                    </q-date>
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>

          <div class="q-pt-sm">
            <q-btn
              dense
              round
              color="primary"
              icon="search"
              @click="onClick"
              :disable="desde == '' || hasta == ''"
            >
              <q-tooltip
                transition-show="scale"
                transition-hide="scale"
                class="bg-amber text-caption text-black shadow-4"
                :offset="[10, 10]"
              >
                Buscar por rango de fecha
              </q-tooltip>
            </q-btn>
          </div>
        </div>
        <div class="q-ma-sm">
          <q-input
            outlined
            input-class="text-right"
            dense
            debounce="350"
            borderless
            :color="appStore.darkMode ? 'info' : 'primary'"
            v-model="filter"
            placeholder="Buscar..."
          >
            <template v-slot:append>
              <q-icon v-if="filter === ''" name="search" />
              <q-icon
                v-else
                name="clear"
                class="cursor-pointer"
                @click="filter = ''"
              />
            </template>
          </q-input>
        </div>
      </template>

      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th auto-width />
          <q-th
            :style="
              appStore.darkMode
                ? 'text-align: center; font-weight: bold; color: #68afed'
                : 'text-align: center; font-weight: bold; color: #1976d2'
            "
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.label }}
          </q-th>
        </q-tr>
      </template>

      <template v-slot:body="props">
        <q-tr :props="props">
          <q-td auto-width>
            <q-btn
              size="sm"
              :color="appStore.darkMode ? 'info' : 'primary'"
              round
              unelevated
              dense
              icon="print"
              @click="imprimirRecibo(props.row)"
            >
              <q-tooltip class="text-body2" :offset="[10, 10]">
                Reimprimir recibo
              </q-tooltip>
            </q-btn>
          </q-td>
          <q-td
            :style="
              appStore.darkMode
                ? 'text-align: center; color: #ffffff'
                : 'text-align: center; color: #000000'
            "
            v-for="col in props.cols"
            :key="col.name"
            :props="props"
          >
            {{ col.value }}
          </q-td>
        </q-tr>
      </template>
    </q-table>
    <div class="row justify-center q-mt-md">
      <q-pagination
        v-if="pagesNumber > 1"
        size="12px"
        v-model="page"
        :max="pagesNumber"
        :max-pages="7"
        direction-links
        boundary-links
        icon-first="skip_previous"
        icon-last="skip_next"
        icon-prev="fast_rewind"
        icon-next="fast_forward"
        text-color="grey"
        color="primary"
        unelevated
        flat
        active-design="flat"
        active-color="white"
        active-text-color="primary"
      />
    </div>
  </div>
</template>
