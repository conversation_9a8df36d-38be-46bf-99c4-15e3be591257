# name: Deploy Quasar App to GCP

# on:
#   push:
#     branches: [main]
#   workflow_dispatch:

# jobs:
#   deploy:
#     runs-on: ubuntu-latest

#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4

#       - name: Setup SSH key
#         run: |
#           mkdir -p ~/.ssh
#           echo "${{ secrets.GCP_SERVER_SSH_KEY }}" > ~/.ssh/id_ed25519
#           chmod 600 ~/.ssh/id_ed25519
#           ssh-keyscan -H ${{ secrets.GCP_SERVER_HOST }} >> ~/.ssh/known_hosts

#       - name: Create deployment directory on server
#         run: |
#           ssh -i ~/.ssh/id_ed25519 ${{ secrets.GCP_SERVER_USER }}@${{ secrets.GCP_SERVER_HOST }} '
#             mkdir -p ~/quasar-deploy
#           '

#       - name: Copy project files to server
#         run: |
#           scp -i ~/.ssh/id_ed25519 -r ./* ${{ secrets.GCP_SERVER_USER }}@${{ secrets.GCP_SERVER_HOST }}:~/quasar-deploy/

#       - name: Deploy application
#         run: |
#           ssh -i ~/.ssh/id_ed25519 ${{ secrets.GCP_SERVER_USER }}@${{ secrets.GCP_SERVER_HOST }} '
#             cd ~/quasar-deploy

#             # Detener y eliminar contenedor existente si existe
#             if [ $(docker ps -q -f name=recargasapp-nginx) ]; then
#               echo "Deteniendo contenedor existente..."
#               docker stop recargasapp-nginx
#             fi

#             if [ $(docker ps -aq -f name=recargasapp-nginx) ]; then
#               echo "Eliminando contenedor existente..."
#               docker rm recargasapp-nginx
#             fi

#             # Eliminar imagen anterior si existe
#             if [ $(docker images -q recargasapp-nginx) ]; then
#               echo "Eliminando imagen anterior..."
#               docker rmi recargasapp-nginx
#             fi

#             # Construir nueva imagen
#             echo "Construyendo nueva imagen..."
#             docker build -t recargasapp-nginx .

#             # Ejecutar nuevo contenedor
#             echo "Ejecutando nuevo contenedor..."
#             docker run -d -p 8080:80 --name recargasapp-nginx recargasapp-nginx

#             # Verificar que el contenedor esté corriendo
#             if [ $(docker ps -q -f name=recargasapp-nginx) ]; then
#               echo "✅ Despliegue exitoso! La aplicación está corriendo en el puerto 8080"
#             else
#               echo "❌ Error: El contenedor no se pudo iniciar"
#               exit 1
#             fi

#             # Limpiar imágenes no utilizadas
#             docker image prune -f
#           '

#       - name: Verify deployment
#         run: |
#           echo "🚀 Aplicación desplegada exitosamente!"
#           echo "📍 Servidor: ${{ secrets.GCP_SERVER_HOST }}"
#           echo "🌐 URL: http://${{ secrets.GCP_SERVER_HOST }}:8080"

name: Deploy Quasar Frontend to GCP

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.GCP_SERVER_SSH_KEY }}" > ~/.ssh/id_ed25519
          chmod 600 ~/.ssh/id_ed25519
          # Configurar SSH
          cat > ~/.ssh/config << 'SSHCONFIG'
          Host ${{ secrets.GCP_SERVER_HOST }}
            HostName ${{ secrets.GCP_SERVER_HOST }}
            User ${{ secrets.GCP_SERVER_USER }}
            IdentityFile ~/.ssh/id_ed25519
            StrictHostKeyChecking no
            UserKnownHostsFile /dev/null
            LogLevel ERROR
          SSHCONFIG
          chmod 600 ~/.ssh/config

      - name: Create frontend deployment directory on server
        run: |
          ssh ${{ secrets.GCP_SERVER_HOST }} '
            mkdir -p ~/recargasapp/frontend
          '

      - name: Copy frontend project files to server
        run: |
          scp -r ./* ${{ secrets.GCP_SERVER_HOST }}:~/recargasapp/frontend/

      - name: Deploy frontend application
        run: |
          ssh ${{ secrets.GCP_SERVER_HOST }} '
            cd ~/recargasapp/frontend

            # Crear la red si no existe
            if ! sudo docker network ls | grep -q "recargasapp-network"; then
              echo "Creando red recargasapp-network..."
              sudo docker network create recargasapp-network
            fi

            # Detener y eliminar contenedor frontend existente si existe
            if [ $(sudo docker ps -q -f name=recargasapp-frontend) ]; then
              echo "Deteniendo contenedor frontend existente..."
              sudo docker stop recargasapp-frontend
            fi

            if [ $(sudo docker ps -aq -f name=recargasapp-frontend) ]; then
              echo "Eliminando contenedor frontend existente..."
              sudo docker rm recargasapp-frontend
            fi

            # Eliminar imagen anterior si existe
            if [ $(sudo docker images -q recargasapp-frontend) ]; then
              echo "Eliminando imagen frontend anterior..."
              sudo docker rmi recargasapp-frontend
            fi

            # Construir nueva imagen frontend con mejor manejo de errores
            echo "Construyendo nueva imagen frontend..."
            if ! sudo docker build -t recargasapp-frontend . --no-cache; then
              echo "❌ Error en el build de la imagen"
              exit 1
            fi

            # Verificar que la imagen se creó correctamente
            if ! sudo docker images | grep -q "recargasapp-frontend"; then
              echo "❌ Error: La imagen no se creó correctamente"
              exit 1
            fi

            # Ejecutar nuevo contenedor frontend
            echo "Ejecutando nuevo contenedor frontend..."
            sudo docker run -d \
              -p 8080:80 \
              --name recargasapp-frontend \
              --network recargasapp-network \
              --restart unless-stopped \
              recargasapp-frontend

            # Verificar que el contenedor esté corriendo
            if [ $(sudo docker ps -q -f name=recargasapp-frontend) ]; then
              echo "✅ Despliegue frontend exitoso! La aplicación está corriendo en el puerto 8080"

              # Mostrar logs del contenedor
              echo "📋 Logs del contenedor:"
              sudo docker logs recargasapp-frontend --tail 10

              # Verificar salud del contenedor
              echo "🏥 Estado del contenedor:"
              sudo docker ps -f name=recargasapp-frontend
            else
              echo "❌ Error: El contenedor frontend no se pudo iniciar"
              echo "📋 Logs del contenedor:"
              sudo docker logs recargasapp-frontend
              exit 1
            fi

            # Limpiar imágenes no utilizadas
            sudo docker image prune -f
          '

      - name: Verify frontend deployment
        run: |
          echo "🚀 Frontend desplegado exitosamente!"
          echo "📍 Servidor: ${{ secrets.GCP_SERVER_HOST }}"
          echo "🌐 Frontend URL: http://${{ secrets.GCP_SERVER_HOST }}:8080"
          echo "📁 Directorio: ~/recargasapp/frontend"

      - name: Clean up
        run: |
          rm -f ~/.ssh/id_ed25519
          rm -f ~/.ssh/config
