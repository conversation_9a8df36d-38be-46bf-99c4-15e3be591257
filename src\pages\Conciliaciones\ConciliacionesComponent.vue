<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useAxios } from 'src/services/useAxios';
import { useMensajes } from 'src/services/useMensajes';
import { getCurrentDate } from 'src/services/useWorkDays';
import {
  obtenerSucursales,
  obtenerPvPtotal,
  obtenerPvPtotalFechas,
} from 'src/services/useRecargas';
import TablaConciliaciones from 'src/components/TablaConciliaciones.vue';
import ParametosConciliaciones from 'src/components/ParametrosConciliaciones.vue';
import {
  SucursalesObject,
  ObjetoConciliaciones,
  RespuestaConciliaciones,
} from 'src/components/models';

// Data
const date = ref('');
const desde = ref('');
const hasta = ref('');
const pvp_total = ref(0);
const blueModel = ref(false);
const filter = ref('');
const { get } = useAxios();
const { mostrarError } = useMensajes();
const filas = ref<ObjetoConciliaciones[]>([]);
const sucursales = ref<SucursalesObject[]>([]);
const sucursal = ref<SucursalesObject>({ alm_nomcom: '', pos: '' });
const titulo = 'Total de ventas';
const columnasVisibles = ref([
  'numeroFila',
  'cadena',
  'proveedor',
  'producto',
  'pos',
  'fecha',
  'hora',
  'pvp',
  'autorizacion',
  'telefono',
  'factura',
]);

// Methods
const obtenerConciliaciones = async (date: string, po: string) => {
  const respuesta: RespuestaConciliaciones = await get(
    '/listar_conciliaciones',
    {
      fecha: date,
      pos: po,
    }
  );
  if (respuesta.error === 'S') {
    filas.value = [];
    mostrarError(respuesta.mensaje, undefined);
    return;
  }
  // Check if the response contains data
  if (respuesta.objetos.length === 0) {
    filas.value = [];
  } else {
    filas.value = respuesta.objetos;
    pvp_total.value = await obtenerPvPtotal(date, sucursal.value.pos, 1);
  }
};

const obtenerConciliacionesFechas = async (
  from: string,
  to: string,
  po: string
) => {
  const respuesta: RespuestaConciliaciones = await get(
    '/listar_conciliaciones_fechas',
    {
      fecha_desde: from,
      fecha_hasta: to,
      pos: po,
    }
  );
  if (respuesta.error === 'S') {
    filas.value = [];
    mostrarError(respuesta.mensaje, undefined);
    return;
  }
  // Check if the response contains data
  if (respuesta.objetos.length === 0) {
    filas.value = [];
  } else {
    filas.value = respuesta.objetos;
    pvp_total.value = await obtenerPvPtotalFechas(
      from,
      to,
      sucursal.value.pos,
      1
    );
  }
};

onMounted(async () => {
  date.value = getCurrentDate();
  sucursales.value = await obtenerSucursales();
  obtenerConciliaciones(date.value, sucursal.value.pos);
  pvp_total.value = await obtenerPvPtotal(date.value, sucursal.value.pos, 1);

  // Agregar una sucursal vacía al principio del array
  sucursales.value.unshift({ alm_nomcom: 'TODAS', pos: '' });
});

watch(blueModel, () => {
  filas.value = []; // Limpia las filas
  pvp_total.value = 0; // Resetea el total de PVP
});
</script>

<template>
  <ParametosConciliaciones
    v-model:date="date"
    v-model:desde="desde"
    v-model:hasta="hasta"
    v-model:filter="filter"
    v-model:sucursal="sucursal"
    v-model:blueModel="blueModel"
    v-model:sucursales="sucursales"
    @procesarConciliaciones="obtenerConciliaciones"
    @procesarConciliacionesFechas="obtenerConciliacionesFechas"
  />

  <TablaConciliaciones
    v-model:date="date"
    v-model:desde="desde"
    v-model:hasta="hasta"
    v-model:filter="filter"
    v-model:titulo="titulo"
    v-model:pvp_total="pvp_total"
    v-model:blueModel="blueModel"
    v-model:filas="filas"
    v-model:sucursales="sucursales"
    v-model:columnasVisibles="columnasVisibles"
  />
</template>
