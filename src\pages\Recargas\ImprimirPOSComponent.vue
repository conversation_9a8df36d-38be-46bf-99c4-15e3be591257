<script setup lang="ts">
import { convertHtmlPdf } from '../../services/useRecargas';

const ruc = defineModel<string>('ruc', { required: true });
const valor = defineModel<number>('valor', { required: true });
const fecha = defineModel<string>('fecha', { required: true });
const numero = defineModel<string>('numero', { required: true });
const factura = defineModel<string>('factura', {
  required: true,
  default: '001-003-14372',
});
const telefono = defineModel<string>('telefono', { required: true });
const producto = defineModel<string>('producto', { required: true });
const imprimir = defineModel<boolean>('imprimir', { required: true });
const proveedor = defineModel<string>('proveedor', { required: true });
const direccion = defineModel<string>('direccion', { required: true });
const razonSocial = defineModel<string>('razonSocial', { required: true });

/* defined emits*/
const emit = defineEmits(['cerrarFactura']);

// Methods
const Imprimir = () => {
  convertHtmlPdf(
    ruc.value,
    valor.value,
    fecha.value,
    numero.value,
    factura.value,
    telefono.value,
    producto.value,
    proveedor.value,
    direccion.value,
    razonSocial.value
  );
  emit('cerrarFactura');
};
</script>

<template>
  <div class="column q-ma-md" v-if="imprimir">
    <q-btn color="primary" @click="Imprimir" label="Imprimir" />
  </div>
</template>

<style lang="scss" scoped></style>
