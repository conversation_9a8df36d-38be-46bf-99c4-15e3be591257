# server {
#     listen 80;
#     server_name localhost;
#     root /usr/share/nginx/html;
#     index index.html;

#     # Configuración para SPA - todas las rutas van a index.html
#     location / {
#         try_files $uri $uri/ /index.html;
#     }

#     # Configuración para archivos estáticos
#     location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#     }

#     # Configuración de seguridad
#     add_header X-Frame-Options "SAMEORIGIN" always;
#     add_header X-Content-Type-Options "nosniff" always;
#     add_header X-XSS-Protection "1; mode=block" always;

#     # Compresión gzip
#     gzip on;
#     gzip_vary on;
#     gzip_min_length 1024;
#     gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
# }

server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # Configuración clave para SPA
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Archivos estáticos
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # No cachear index.html
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Configuración de seguridad
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}


# server {
#     listen 80;
#     server_name localhost;

#     # Ubicación del contenido estático
#     root /usr/share/nginx/html;
#     index index.html;

#     # Configuración para SPA - todas las rutas van a index.html
#     location / {
#         try_files $uri $uri/ /index.html;
#     }

#     # Configuración para archivos estáticos
#     location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
#         expires 1y;
#         add_header Cache-Control "public, immutable";
#         try_files $uri =404;
#     }

#     # Configuración de seguridad
#     add_header X-Frame-Options "SAMEORIGIN" always;
#     add_header X-Content-Type-Options "nosniff" always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header Referrer-Policy "strict-origin-when-cross-origin" always;

#     # Compresión gzip
#     gzip on;
#     gzip_vary on;
#     gzip_min_length 1024;
#     gzip_proxied expired no-cache no-store private must-revalidate auth;
#     gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

#     # Health check endpoint para Docker
#     location /health {
#         access_log off;
#         return 200 "healthy\n";
#         add_header Content-Type text/plain;
#     }

#     # Configuración de logs
#     access_log /var/log/nginx/access.log;
#     error_log /var/log/nginx/error.log;

#     # Configuración de errores
#     error_page 404 /index.html;
#     error_page 500 502 503 504 /50x.html;

#     location = /50x.html {
#         root /usr/share/nginx/html;
#     }
# }
