# Multi-stage build for Quasar frontend
# FROM node:20-alpine AS builder

# # Set working directory
# WORKDIR /app

# # Install system dependencies
# RUN apk add --no-cache git python3 make g++

# # Copy package files
# COPY package*.json ./
# COPY .eslintignore ./
# COPY .eslintrc.cjs ./

# # Configure npm for better network reliability
# RUN npm config set registry https://registry.npmjs.org/ && \
#     npm config set fetch-retries 10 && \
#     npm config set fetch-retry-mintimeout 30000 && \
#     npm config set fetch-retry-maxtimeout 300000 && \
#     npm config set fetch-timeout 600000

# # Clear cache and install dependencies with fallback
# RUN npm cache clean --force && \
#     (npm ci --no-audit --prefer-offline || \
#      (echo "npm ci failed, trying npm install..." && \
#       rm -rf node_modules package-lock.json && \
#       npm install --no-audit --legacy-peer-deps))

# # Copy source code
# COPY . .

# # Build the application
# RUN npm run build

# # Production stage with Nginx
# FROM nginx:alpine

# # Install wget for health check
# RUN apk add --no-cache wget

# # Copy built application from builder stage
# COPY --from=builder /app/dist/spa /usr/share/nginx/html

# # Copy nginx configuration
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# # Expose port
# EXPOSE 80

# # Health check
# HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
#     CMD wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

# # Run nginx
# CMD ["nginx", "-g", "daemon off;"]






# Multi-stage build for Quasar frontend
# Build Stage
FROM node:20-alpine AS build-stage
WORKDIR /app
COPY package*.json ./
COPY .eslintignore ./
COPY .eslintrc.cjs ./
RUN npm install
COPY . .
RUN npm run build

# Production stage with Nginx
FROM nginx:stable-alpine AS production-stage
COPY --from=build-stage /app/dist/spa /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
