


# Multi-stage build for Quasar frontend
# Build Stage
FROM node:20-alpine AS build-stage
WORKDIR /app
COPY package*.json ./
COPY .eslintignore ./
COPY .eslintrc.cjs ./
RUN npm install
COPY . .
RUN npm run build

# Production stage with Nginx
FROM nginx:stable-alpine AS production-stage

# Install wget for health checks
RUN apk add --no-cache wget

# Copy built application from build stage
COPY --from=build-stage /app/dist/spa /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

# Run nginx
CMD ["nginx", "-g", "daemon off;"]
