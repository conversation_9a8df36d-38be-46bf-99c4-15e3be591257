<script setup lang="ts">
import { ref } from 'vue';
import { useAppStore } from '../../stores/app-store';
import PaquetesComponent from './PaquetesComponent.vue';
import TiempoAireComponent from './TiempoAireComponent.vue';

// Data
const tab = ref('');
const appStore = useAppStore();
</script>

<template>
  <div class="q-pt-sm">
    <h4
      class="row text-uppercase justify-center content-center q-my-sm q-pb-md"
      style="font-family: 'Bebas Neue'"
    >
      <div class="q-pt-sm gt-xs">RECARGAS DE PAQUETES Y TIEMPO AIRE</div>
      <div class="q-pt-sm xs">RECARGAS</div>
    </h4>
  </div>
  <div class="q-gutter-y-sm">
    <q-card flat>
      <q-tabs
        v-model="tab"
        dense
        :active-color="appStore.darkMode ? 'info' : 'primary'"
        :indicator-color="appStore.darkMode ? 'info' : 'primary'"
        style="font-family: 'Oswald'"
        align="justify"
        narrow-indicator
      >
        <q-tab name="paquetes" label="Paquetes de datos" />
        <q-tab name="aire" label="Saldo (Tiempo aire)" />
      </q-tabs>

      <q-separator />

      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="paquetes">
          <PaquetesComponent />
        </q-tab-panel>
        <q-tab-panel name="aire">
          <TiempoAireComponent />
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </div>
</template>
