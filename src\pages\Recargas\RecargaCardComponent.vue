<script setup lang="ts">
import { ref } from 'vue';
import { useAppStore } from '../../stores/app-store';
import RecargaComponent from './RecargaComponent.vue';

const vigencia = defineModel<number>('vigencia', { required: true });
const proveedor = defineModel<string>('proveedor', { required: true });
const producto = defineModel<string>('producto', { required: true });
const valor = defineModel<number>('valor', { required: true });
const datos = defineModel<string>('datos', { required: true });
const descripcion = defineModel<string>('descripcion', { required: true });
const nuevo = defineModel<boolean>('nuevo', { required: true });
const imagen = defineModel<string>('imagen', { required: true });

// Data
const comprar = ref(false);
const appStore = useAppStore();
</script>

<template>
  <q-card class="my-card" flat bordered>
    <q-item>
      <q-item-section avatar>
        <q-avatar rounded>
          <img :src="imagen" />
          <q-badge floating color="warning" v-if="nuevo">Nuevo</q-badge>
        </q-avatar>
      </q-item-section>

      <q-item-section>
        <q-item-label>
          <span
            :class="
              appStore.darkMode
                ? 'q-mt-xs text-body2 text-weight-bold text-uppercase text-info'
                : 'q-mt-xs text-body2 text-weight-bold text-primary text-uppercase'
            "
            >{{ datos }}</span
          >
        </q-item-label>

        <q-item-label lines="1"
          >Vigencia:
          {{
            vigencia < 1
              ? (vigencia * 10).toFixed(0) + ' horas'
              : vigencia + (vigencia > 1 ? ' días' : ' día')
          }}</q-item-label
        >
      </q-item-section>
    </q-item>

    <q-card-section class="q-pt-none">
      <div class="text-subtitle1">$ {{ valor.toFixed(2) }} (Inc. Impuesto)</div>
      <div
        :class="
          appStore.darkMode
            ? 'text-caption text-grey'
            : 'text-caption text-grey-9'
        "
      >
        {{ descripcion }}
      </div>
    </q-card-section>

    <q-separator />

    <q-card-actions>
      <q-btn flat color="positive" label="COMPRAR" @click="comprar = true" />
    </q-card-actions>
  </q-card>
  <RecargaComponent
    v-model:proveedor="proveedor"
    v-model:producto="producto"
    v-model:valor.number="valor"
    v-model:comprar="comprar"
    v-model:descripcion="descripcion"
    v-model:nuevo="nuevo"
    v-model:imagen="imagen"
  />
</template>
<style lang="scss" scoped>
.my-card {
  width: 100%;
  max-width: 279px;
}
</style>
