export const meses = [
  '<PERSON><PERSON>',
  'Febrero',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  'Agosto',
  'Septiembre',
  'Octubre',
  'Noviembre',
  'Diciembre',
];

export const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

export const getCurrentYear = (): number => {
  const currentDate = new Date();
  return currentDate.getFullYear();
};

export function useYearOptions() {
  const yearOptions: number[] = [];
  const currentYear = getCurrentYear();
  const maxYear = currentYear + 2;
  for (let year = currentYear - 2; year <= maxYear; year++) {
    yearOptions.push(year);
  }
  return yearOptions;
}

export const formatearFecha = (date: string) => {
  const fecha = new Date(date);

  // Array con los nombres de los días de la semana y los meses
  const diasSemana = [
    'Domingo',
    'Lunes',
    'Martes',
    'Miércoles',
    'Jueves',
    'Viernes',
    'Sábado',
  ];
  const meses = [
    'enero',
    'febrero',
    'marzo',
    'abril',
    'mayo',
    'junio',
    'julio',
    'agosto',
    'septiembre',
    'octubre',
    'noviembre',
    'diciembre',
  ];

  // Obtener el día de la semana, el día del mes y el mes
  const diaSemana = diasSemana[fecha.getDay()]; // Sin sumar 1
  const dia = fecha.getDate(); // Sin sumar 1
  const mes = meses[fecha.getMonth()];
  const año = fecha.getFullYear();

  // Formatear la fecha como "día de la semana, día de mes de año"
  const fechaFormateada = diaSemana + ', ' + dia + ' de ' + mes + ' de ' + año;

  return fechaFormateada;
};

export const normalizarTexto = (texto: string) => {
  return texto
    .toUpperCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/Ñ/g, 'N');
};
