<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { useAppStore } from 'src/stores/app-store';
import { CuentasObject } from 'src/components/models';
import { handleResponse } from 'src/utils/AppUtils';
import {
  obtenerCuentas,
  registrarTransferencia,
} from 'src/services/useRecargas';

// Data
const appStore = useAppStore();
const comprobante = ref<string>('');
const cuentas = ref<CuentasObject[]>([]);
const valor = defineModel<number>('valor', { required: true });
const secuencial = defineModel<number>('secuencial', { required: true });
const fecha = defineModel<string>('fecha', { required: false });
const transferencia = defineModel<boolean>('transferencia', { required: true });
const cuenta = ref<CuentasObject>({
  bct_descri: 'BANCO DE LOJA 2903807463',
  bct_codigo: '21',
});

const emit = defineEmits(['transferencia-registrada']);

// Methods
onMounted(async () => {
  cuentas.value = await obtenerCuentas();
  if (!fecha.value) {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    fecha.value = `${year}-${month}-${day}`;
  }
});

watch(transferencia, (newVal) => {
  if (newVal) {
    cuenta.value = {
      bct_descri: 'BANCO DE LOJA 2903807463',
      bct_codigo: '21',
    };
    comprobante.value = '';
  }
});

const handleRegistrar = async () => {
  const response = await registrarTransferencia(
    parseInt(cuenta.value.bct_codigo),
    comprobante.value,
    secuencial.value
  );

  if (response.error == 'N') {
    transferencia.value = false;
    handleResponse(response);
    emit('transferencia-registrada');
  }
};
</script>

<template>
  <q-dialog v-model="transferencia" persistent>
    <q-card class="my-card" flat bordered>
      <q-card-section>
        <span class="text-h6">Datos de la Transferencia</span>
      </q-card-section>

      <q-card-section>
        <div style="width: 350px">
          <q-select
            dense
            outlined
            v-model="cuenta"
            :options="cuentas"
            option-value="bct_codigo"
            option-label="bct_descri"
            label="Cuenta de banco"
          />
        </div>
      </q-card-section>

      <q-card-section horizontal>
        <q-card-section>
          <q-input
            filled
            dense
            :color="appStore.darkMode ? 'info' : 'primary'"
            v-model="fecha"
            mask="date"
            :rules="['date']"
            label="Seleccione la fecha"
          >
            <template v-slot:append>
              <q-icon name="event" class="cursor-pointer">
                <q-popup-proxy
                  cover
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-date v-model="fecha">
                    <div class="row items-center justify-end">
                      <q-btn
                        v-close-popup
                        label="Cerrar"
                        color="primary"
                        flat
                      />
                    </div>
                  </q-date>
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </q-card-section>

        <q-card-section>
          <q-input
            filled
            square
            v-model.number="valor"
            type="number"
            dense
            disable
            label="Monto"
          />
        </q-card-section>
      </q-card-section>

      <q-card-section class="q-pt-xs">
        <q-input
          filled
          square
          dense
          v-model="comprobante"
          label="Número de comprobante"
          hint="Ej: 60016844"
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Cancelar" @click="transferencia = false" />
        <q-btn color="primary" label="Aceptar" @click="handleRegistrar()" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style lang="scss" scoped>
.my-card {
  width: 100%;
  max-width: 379px;
}
</style>
