<script setup lang="ts">
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import { computed, ref, watch } from 'vue';
import { useAxios } from 'src/services/useAxios';
import { cedRucRule } from 'src/services/useReglas';
import { useAppStore } from '../../stores/app-store';
import { getRecarga } from 'src/services/useRecargas';
import { useMensajes } from '../../services/useMensajes';
import ImprimirPOSComponent from './ImprimirPOSComponent.vue';
import { deducirMensajeError, handleResponse } from '../../utils/AppUtils';
import {
  DatosFactura,
  ObjectError,
  RespuestaFactura,
  TokenDecoded,
} from '../../components/models';
import { Loading, useQuasar, QSpinnerFacebook } from 'quasar';

const { mostrarError } = useMensajes();
const valor = defineModel<number>('valor', { required: true });
const fecha = defineModel<string>('fecha', { required: true });
const numero = defineModel<string>('numero', { required: true });
const codigo = defineModel<number>('codigo', { required: true });
const comprar = defineModel<boolean>('comprar', { required: true });
const producto = defineModel<string>('producto', { required: true });
const facturar = defineModel<boolean>('facturar', { required: true });
const proveedor = defineModel<string>('proveedor', { required: true });
const autorizacionRecarga = defineModel<string>('autorizacionRecarga', {
  required: false,
  default: '',
});

// Data
const ruc = ref('');
const email = ref('');
const $q = useQuasar();
const factura = ref('');
const telefono = ref('');
const trn_codigo = ref(0);
const direccion = ref('');
const eleccion = ref(null);
const imprimir = ref(false);
const razonSocial = ref('');
const facturado = ref(false);
const appStore = useAppStore();
const { get, put } = useAxios();

const emailRule: ((v: string) => string | boolean)[] = [
  (v: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v) ||
    'Formato de correo electrónico inválido',
];

const opciones = [
  { label: 'Consumidor final', value: 0 },
  { label: 'Datos', value: 1, color: 'green' },
];

// Methods
const generarFactura = async () => {
  try {
    Loading.show({
      spinner: QSpinnerFacebook,
      message: 'Procesando factura...',
    });

    const response: RespuestaFactura = await put(
      '/registrar_factura',
      {},
      {
        codigo: codigo.value,
        autorizacion:
          autorizacionRecarga.value !== null ? autorizacionRecarga.value : '',
        ced_ruc: eleccion.value === 0 ? '9999999999999' : ruc.value,
        celular: telefono.value,
        clp_calles: direccion.value,
        clp_descri: razonSocial.value,
        email: email.value,
        usu_codigo: appStore.codigo,
        alm_codigo: appStore.alm_codigo,
        valor: valor.value,
        recarga: getRecarga(producto.value, proveedor.value),
        factura: factura.value,
      }
    );

    if (response.error == 'N') {
      const token = appStore.token;
      const codigoFactura = response.objetos[0]?.codigo;
      const decodedToken = jwtDecode<TokenDecoded>(token);

      if (codigoFactura) {
        try {
          // Configuramos un timeout de 30 segundos
          const url = `${appStore.appUrl}/comun/transaccion/crear_factura_recarga?codigo=${codigoFactura}`;
          const headers = {
            login: appStore.login,
            clave: decodedToken.password,
            codigo_empresa: appStore.empresa.codigo_empresa,
          };

          const apiResponse = await axios.get(url, {
            headers,
            timeout: 30000, // 30 segundos en milisegundos
          });

          if (apiResponse.data.error === 'N') {
            factura.value = apiResponse.data.objetos[1];

            const response2: RespuestaFactura = await put(
              '/registrar_factura',
              {},
              {
                codigo: codigo.value,
                autorizacion:
                  autorizacionRecarga.value !== null
                    ? autorizacionRecarga.value
                    : '',
                ced_ruc: eleccion.value === 0 ? '9999999999999' : ruc.value,
                celular: telefono.value,
                clp_calles: direccion.value,
                clp_descri: razonSocial.value,
                email: email.value,
                usu_codigo: appStore.codigo,
                alm_codigo: appStore.alm_codigo,
                valor: valor.value,
                recarga: getRecarga(producto.value, proveedor.value),
                factura: factura.value,
              }
            );

            handleResponse(response2);

            if (response2.error == 'N') {
              imprimir.value = true;
              facturado.value = true;
            } else {
              mostrarError(
                'No se pudo generar la factura. ' + response2.mensaje,
                undefined
              );
              return;
            }
          } else {
            mostrarError(
              'No se pudo generar la factura. ' + apiResponse.data.mensaje,
              undefined
            );
            return;
          }
        } catch (apiError) {
          if (
            axios.isAxiosError(apiError) &&
            apiError.code === 'ECONNABORTED'
          ) {
            mostrarError(
              'No se pudo generar la factura. El servicio tardó demasiado en responder.',
              undefined
            );
          } else {
            mostrarError(
              'No se pudo generar la factura. El servicio no responde.',
              undefined
            );
          }
        }
      }
    } else {
      mostrarError('Error al registrar la factura en el sistema.', undefined);
    }
  } catch (error) {
    deducirMensajeError(error as ObjectError);
  } finally {
    Loading.hide();
  }
};

const handleClose = () => {
  resetearDatos();
  facturar.value = false;
  comprar.value = false;
};

const obtenerDatos = async (ruc: string) => {
  const response: DatosFactura = await get('/datos_factura', {
    ced_ruc: ruc,
  });
  if (response.error == 'N') {
    trn_codigo.value = response.objetos.clp_codigo;
    razonSocial.value = response.objetos.clp_descri;
    telefono.value = response.objetos.celular;
    direccion.value = response.objetos.clp_calles;
    email.value = response.objetos.email;
  }
};

const resetearDatos = () => {
  ruc.value = '';
  eleccion.value = null;
  email.value = '';
  telefono.value = '';
  direccion.value = '';
  razonSocial.value = '';
  imprimir.value = false;
  facturado.value = false;
};

watch(eleccion, (newValue) => {
  if (newValue === 1) {
    ruc.value = '';
    email.value = '';
    telefono.value = '';
    direccion.value = '';
    razonSocial.value = '';
  } else {
    ruc.value = '9999999999999';
  }
});

// const rucValido = computed(() => {
//   return ruc.value.length === 10 || ruc.value.length === 13;
// });

const rucValido = computed(() => {
  return (
    ruc.value.length === 10 ||
    (ruc.value.length === 13 &&
      (ruc.value.endsWith('001') || ruc.value === '9999999999999'))
  );
});

watch(ruc, (newRuc) => {
  if (newRuc.length === 13 || newRuc.length === 10) {
    obtenerDatos(newRuc as string);
  }
});
</script>

<template>
  <q-dialog v-model="facturar" persistent>
    <q-card class="my-card" flat bordered>
      <q-card-section class="row items-center q-pb-none">
        <div
          :class="
            appStore.darkMode
              ? 'text-h5 text-info q-mt-sm q-mb-xs'
              : 'text-h5 text-primary q-mt-sm q-mb-xs'
          "
          style="font-family: 'Oswald'"
        >
          Factura
        </div>
        <q-space />
        <q-btn icon="close" flat round dense @click="handleClose" />
      </q-card-section>

      <q-separator />

      <q-card-section class="q-py-xs" :class="$q.screen.xs ? 'q-py-xs' : ''">
        <div
          :class="
            appStore.darkMode
              ? 'text-caption text-white'
              : 'text-caption text-grey-8'
          "
        >
          <p class="gt-xs q-mb-none q-mt-xs">
            <strong>Nota:</strong> Antes de ingresar los datos del cliente para
            la facturación, seleccione una de las siguientes opciones:
          </p>
        </div>

        <div :class="$q.screen.xs ? 'q-pa-xs' : 'q-pa-sm'">
          <q-option-group
            :options="opciones"
            type="radio"
            v-model="eleccion"
            :disable="facturado"
          />
        </div>
      </q-card-section>

      <q-separator inset />

      <q-card-section class="q-pt-md">
        <div
          :class="
            appStore.darkMode
              ? 'text-caption text-white'
              : 'text-caption text-grey-8'
          "
        >
          <p class="gt-xs q-mb-none">
            <strong>Nota:</strong>
          </p>
          <ul>
            <li>¡Ingrese primero la cédula o el RUC!</li>
            <li>
              Los datos marcados con <strong style="color: red">*</strong> son
              obligatorios para generar la factura con datos.
            </li>
            <li>
              {{
                `El monto máximo para facturar a Consumidor Final es de $${appStore.maxConsumidorFinal} dólares`
              }}
            </li>
          </ul>
        </div>

        <div :class="$q.screen.xs ? 'column' : 'row justify-evenly'">
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs">
              <strong>Cédula o RUC:</strong> <span style="color: red">*</span>
            </p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              v-model="ruc"
              :disable="!eleccion"
              :dense="$q.screen.xs ? true : false"
              label="Ingrese la cédula o el RUC"
              :rules="cedRucRule"
            />
          </div>
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs">
              <strong>Razón Social:</strong> <span style="color: red">*</span>
            </p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="razonSocial"
              :dense="$q.screen.xs ? true : false"
              label="Nombre o Razón Social"
              :disable="!eleccion"
            />
          </div>
        </div>
        <div :class="$q.screen.xs ? 'column q-mt-md' : 'row justify-evenly'">
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs"><strong>Email:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              v-model="email"
              :dense="$q.screen.xs ? true : false"
              label="Email"
              :rules="emailRule"
              :disable="!eleccion"
            />
          </div>
          <div class="column col-5">
            <p class="q-pb-xs q-mb-xs gt-xs"><strong>Teléfono:</strong></p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              v-model="telefono"
              :dense="$q.screen.xs ? true : false"
              label="Número de teléfono o celular"
              :disable="!eleccion"
            />
          </div>
        </div>
        <div class="row justify-evenly">
          <div
            class="column col-11"
            :class="$q.screen.xs ? 'column col-12 q-mt-md' : 'column col-11'"
          >
            <p class="q-pb-xs q-mb-xs gt-xs">
              <strong>Dirección: </strong> <span style="color: red">*</span>
            </p>
            <q-input
              outlined
              :color="appStore.darkMode ? 'info' : 'primary'"
              debounce="1000"
              v-model="direccion"
              :dense="$q.screen.xs ? true : false"
              label="Dirección"
              :disable="!eleccion"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <q-card-actions align="center">
        <q-btn
          color="positive"
          class="q-mt-md q-px-md"
          style="width: 100%"
          :disabled="
            eleccion === null ||
            !rucValido ||
            razonSocial === '' ||
            direccion === '' ||
            (eleccion === 0 && valor >= 7)
          "
          @click="generarFactura"
          v-if="!facturado"
        >
          Generar Factura

          <q-tooltip
            transition-show="scale"
            transition-hide="scale"
            class="bg-amber text-caption text-black shadow-4"
            :offset="[10, 10]"
            v-if="eleccion === 0 && valor >= 7"
          >
            {{
              `El monto máximo para facturar a Consumidor Final es de $${appStore.maxConsumidorFinal} dólares`
            }}
          </q-tooltip>
        </q-btn>
        <ImprimirPOSComponent
          class="col-6"
          v-model:ruc="ruc"
          v-model:valor="valor"
          v-model:fecha="fecha"
          v-model:numero="numero"
          v-model:factura="factura"
          v-model:telefono="telefono"
          v-model:producto="producto"
          v-model:imprimir="imprimir"
          v-model:proveedor="proveedor"
          v-model:direccion="direccion"
          v-model:razonSocial="razonSocial"
          @actualizarAutorizacion="resetearDatos"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<style lang="scss" scoped>
.my-card {
  width: 100%;
  max-width: 600px;
}
</style>
