<script setup lang="ts">
import { useQuasar } from 'quasar';
import { computed, ref } from 'vue';
import RecargaCard from './RecargaCardComponent.vue';
import { operadores } from '../../components/operadores';

// Data
const $q = useQuasar();
const mostrarClaro = ref(false);
const mostrarMovistar = ref(false);
const mostrarTuenti = ref(false);
const mostrarCNT = ref(false);
const mostrarCNTa = ref(false);
const mostrarMaxiplus = ref(false);
const mostrarAkimovil = ref(false);
const filter = ref(''); // Filtro para buscar

// Methods
const activarOperador = (operador: string) => {
  mostrarClaro.value = false;
  mostrarMovistar.value = false;
  mostrarTuenti.value = false;
  mostrarCNT.value = false;
  mostrarCNTa.value = false;
  mostrarMaxiplus.value = false;
  mostrarAkimovil.value = false;

  switch (operador) {
    case 'claro':
      mostrarClaro.value = true;
      break;
    case 'movistar':
      mostrarMovistar.value = true;
      break;
    case 'tuenti':
      mostrarTuenti.value = true;
      break;
    case 'cnt':
      mostrarCNT.value = true;
      break;
    case 'cnta':
      mostrarCNTa.value = true;
      break;
    case 'maxiplus':
      mostrarMaxiplus.value = true;
      break;
    case 'akimovil':
      mostrarAkimovil.value = true;
      break;
  }
};

// Computed property to filter packages based on search input
const filteredPaquetes = computed(() => {
  const valorBuscado = filter.value; // No convertimos a número, trabajamos con el texto

  if (mostrarClaro.value) {
    return filter.value === ''
      ? operadores[0].paquetes // Devuelve todos los paquetes si no hay filtro
      : operadores[0].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  if (mostrarMovistar.value) {
    return filter.value === ''
      ? operadores[1].paquetes
      : operadores[1].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  if (mostrarTuenti.value) {
    return filter.value === ''
      ? operadores[2].paquetes
      : operadores[2].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  if (mostrarCNT.value) {
    return filter.value === ''
      ? operadores[3].paquetes
      : operadores[3].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  if (mostrarCNTa.value) {
    return filter.value === ''
      ? operadores[4].paquetes
      : operadores[4].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  if (mostrarMaxiplus.value) {
    return filter.value === ''
      ? operadores[5].paquetes
      : operadores[5].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  if (mostrarAkimovil.value) {
    return filter.value === ''
      ? operadores[6].paquetes
      : operadores[6].paquetes.filter((paquete) =>
          paquete.valor.toString().includes(valorBuscado)
        );
  }
  return [];
});
</script>

<template>
  <div class="row wrap justify-evenly q-my-md">
    <q-btn
      v-for="(operador, index) in operadores"
      :key="index"
      unelevated
      @click="activarOperador(operador.nombre)"
    >
      <q-avatar rounded size="79px">
        <img :src="operador.imagen" />
      </q-avatar>
    </q-btn>
  </div>

  <q-separator inset />

  <div class="q-pa-md">
    <div class="q-gutter-y-md column" style="width: 300px; max-width: 100%">
      <q-toolbar class="bg-primary text-white rounded-borders">
        <q-input
          dark
          dense
          standout
          v-model="filter"
          input-class="text-right"
          class="q-ml-md"
          placeholder="Filtrar por valor (ej. 10.25)"
        >
          <template v-slot:append>
            <q-icon v-if="filter === ''" name="search" />
            <q-icon
              v-else
              name="clear"
              class="cursor-pointer"
              @click="filter = ''"
            />
          </template>
        </q-input>
      </q-toolbar>
    </div>
  </div>

  <div v-if="mostrarClaro">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[0].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[0].imagen"
        />
      </div>
    </div>
  </div>

  <div v-if="mostrarMovistar">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[1].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[1].imagen"
        />
      </div>
    </div>
  </div>

  <div v-if="mostrarTuenti">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[2].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[2].imagen"
        />
      </div>
    </div>
  </div>

  <div v-if="mostrarCNT">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[3].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[3].imagen"
        />
      </div>
    </div>
  </div>

  <div v-if="mostrarCNTa">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[4].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[4].imagen"
        />
      </div>
    </div>
  </div>

  <div v-if="mostrarMaxiplus">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[5].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[5].imagen"
        />
      </div>
    </div>
  </div>

  <div v-if="mostrarAkimovil">
    <div
      :class="
        $q.screen.xs
          ? 'q-pa-md row justify-center items-start q-gutter-md'
          : 'q-pa-md row items-start q-gutter-md'
      "
    >
      <div v-for="(paquete, index) in filteredPaquetes" :key="index">
        <RecargaCard
          v-model:proveedor="operadores[6].proveedor"
          v-model:producto="paquete.producto"
          v-model:vigencia="paquete.vigencia"
          v-model:valor="paquete.valor"
          v-model:datos="paquete.datos"
          v-model:descripcion="paquete.descripcion"
          v-model:nuevo="paquete.nuevo"
          v-model:imagen="operadores[6].imagen"
        />
      </div>
    </div>
  </div>
</template>
