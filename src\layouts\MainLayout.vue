<script setup lang="ts">
import { jwtDecode } from 'jwt-decode';
import { useRouter } from 'vue-router';
import { DarkMode } from '../components/models';
import { useAxios } from 'src/services/useAxios';
import { LocalStorage, useQuasar } from 'quasar';
import { useAppStore } from '../stores/app-store';
import { onBeforeMount, onMounted, ref, watch } from 'vue';
import { Session, TokenDecoded } from '../components/models';

// Data
const usuario = ref('');
const expires = ref('');
const router = useRouter();
const darkMode = ref(false);
const miniState = ref(true);
const { dark } = useQuasar();
const { get } = useAxios();
const persistent = ref(false);
const appStore = useAppStore();
const tokenExpirationTime = ref(0);

const sinFacturar = ref(0);

// Methods
const checkTokenExpiration = () => {
  const token = appStore.token;
  // const decodedToken: TokenDecoded = jwtDecode(token);
  const decodedToken = jwtDecode<TokenDecoded>(token);
  const expirationTimestamp = decodedToken.exp;
  const currentTime = Math.floor(Date.now() / 1000); // Current timestamp in seconds
  tokenExpirationTime.value = expirationTimestamp - currentTime;
  // console.log('[EXPIRATION TIME]: ', tokenExpirationTime);

  if (tokenExpirationTime.value <= 0) {
    // Token has expired, log the user out
    persistent.value = true;
    appStore.estaLogeado = false;
  } else {
    persistent.value = false;
    // Calculate remaining time
    const remainingTime = tokenExpirationTime.value * 1000;
    const remainingMinutes = Math.floor(remainingTime / 60000);
    const remainingSeconds = Math.floor((remainingTime % 60000) / 1000);

    expires.value = `Token expires in ${remainingMinutes} minutes and ${remainingSeconds} seconds`;
  }
};

onMounted(() => {
  const session: Session | null = LocalStorage.getItem('session');
  usuario.value = session?.usuario || '';
  recargasSinFacturar();
});

const cerrarSesion = () => {
  appStore.cerrarSesion();
  router.push('/login');
};

const leftDrawerOpen = ref(false);

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;
}

watch(
  () => dark.isActive,
  (val) => {
    if (appStore.darkMode !== val) {
      appStore.setDarkModeFromLocalStorage(val);
      LocalStorage.set('darkMode', { darkMode: val });
    }
  }
);

onBeforeMount(async () => {
  const session: DarkMode | null = LocalStorage.getItem('darkMode');
  darkMode.value = session?.darkMode || false;
  if (session?.darkMode !== null) {
    dark.set(darkMode.value);
  }
  checkTokenExpiration();
  setInterval(checkTokenExpiration, 300000); // 5 minutes
  setInterval(recargasSinFacturar, 120000); // 2 minutes
});

watch(
  () => tokenExpirationTime.value,
  (newVal) => {
    if (newVal <= 0) {
      persistent.value = true;
      appStore.estaLogeado = false;
    }
  }
);

const recargasSinFacturar = async () => {
  try {
    const response = await get('/numero_recargas_sin_facturar', {
      iso: appStore.iso,
      login: appStore.login,
    });
    if (response.objetos[0].count > 0) {
      sinFacturar.value = response.objetos[0].count;
    } else {
      sinFacturar.value = 0;
    }
    // sinFacturar.value = response.objetos[0].count;
  } catch (error) {
    console.error(error);
    sinFacturar.value = 0;
  }
};
</script>

<template>
  <q-layout view="lHh Lpr lFf">
    <q-header>
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title
          class="text-h5 row text-uppercase"
          style="font-family: 'Bebas Neue'"
        >
          Recargas APP V{{ appStore.version }}
        </q-toolbar-title>

        <div v-if="sinFacturar > 0">
          <router-link to="/recargas_sin_facturar">
            <q-badge color="red" class="animated-move">
              {{
                sinFacturar > 1
                  ? `${sinFacturar} RECARGAS SIN FACTURAR`
                  : `${sinFacturar} RECARGA SIN FACTURAR`
              }}
              <q-icon name="warning" color="white" class="q-ml-xs" />

              <q-tooltip
                transition-show="scale"
                transition-hide="scale"
                class="bg-amber text-caption text-black shadow-4"
                :offset="[10, 10]"
              >
                Facture todas las recargas antes del cierre de caja
              </q-tooltip>
            </q-badge>
          </router-link>
        </div>

        <div class="row items-center content-center q-mr-md">
          <q-toggle
            :model-value="dark.isActive"
            checked-icon="dark_mode"
            unchecked-icon="light_mode"
            size="3rem"
            @update:model-value="(val) => dark.set(val)"
          />
          <q-btn flat dense @click="cerrarSesion">
            <div class="q-mr-sm" v-if="!($q.screen.lt.md || $q.screen.lt.sm)">
              Salir
            </div>
            <q-icon left name="logout" />
          </q-btn>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      :width="320"
      :mini="miniState && !$q.screen.xs"
      @mouseover="miniState = false"
      @mouseout="miniState = true"
    >
      <q-img
        fit="fill"
        class="absolute-top q-pa-sm"
        src="../assets/loxasoluciones.png"
        style="height: 150px"
        v-show="!miniState"
      />
      <q-img
        fit="fill"
        class="absolute-top q-pa-sm xs"
        src="../assets/loxasoluciones.png"
        style="height: 150px"
      />
      <q-scroll-area
        style="
          height: calc(100% - 150px);
          margin-top: 150px;
          border-right: 1px solid #ddd;
        "
      >
        <q-list padding>
          <q-item class="column" v-show="!miniState">
            <q-separator inset />
            <div class="text-center">Bienvenido</div>
            <div class="text-weight-bold text-center">
              {{ usuario }}
            </div>
            <q-separator inset />
          </q-item>

          <q-item clickable v-ripple to="/" active-class="my-menu-link">
            <q-item-section avatar>
              <q-icon name="home" />
            </q-item-section>

            <q-item-section>
              <span class="text-h6" style="font-family: 'Bebas Neue'">
                PÁGINA DE INICIO
              </span>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            to="/recargas"
            active-class="my-menu-link"
            v-show="appStore.APP_ADMIN === 0"
          >
            <q-item-section avatar>
              <div class="row">
                <q-icon size="1em" name="paid" />
                <q-icon size="1.5em" name="smartphone" />
              </div>
            </q-item-section>

            <q-item-section>
              <span class="text-h6" style="font-family: 'Bebas Neue'">
                RECARGAS
              </span>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            to="/recargas_sin_facturar"
            active-class="my-menu-link"
            v-show="appStore.APP_ADMIN === 0"
          >
            <q-item-section avatar>
              <div class="row">
                <q-icon size="1em" name="receipt" />
                <q-icon size="1.5em" name="smartphone" />
              </div>
            </q-item-section>

            <q-item-section>
              <span class="text-h6" style="font-family: 'Bebas Neue'">
                RECARGAS SIN FACTURAR
              </span>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            to="/reimprimir_recibos"
            active-class="my-menu-link"
            v-show="appStore.APP_ADMIN === 0"
          >
            <q-item-section avatar>
              <q-icon name="print" />
            </q-item-section>

            <q-item-section>
              <span class="text-h6" style="font-family: 'Bebas Neue'">
                REIMPRESIÓN DE RECIBOS
              </span>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            to="/conciliaciones"
            active-class="my-menu-link"
            v-show="appStore.APP_ADMIN === 1"
          >
            <q-item-section avatar>
              <q-icon name="article" />
            </q-item-section>

            <q-item-section>
              <span class="text-h6" style="font-family: 'Bebas Neue'">
                CONCILIACIONES
              </span>
            </q-item-section>
          </q-item>

          <q-item
            clickable
            v-ripple
            to="/cambiar_clave"
            active-class="my-menu-link"
          >
            <q-item-section avatar>
              <q-icon name="password" />
            </q-item-section>
            <q-item-section>
              <span class="text-h6" style="font-family: 'Bebas Neue'">
                CAMBIAR CLAVE
              </span>
            </q-item-section>
          </q-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<style scoped>
@keyframes moveAnimation {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(5px); /* Mueve ligeramente a la derecha */
  }
  100% {
    transform: translateX(0);
  }
}

.animated-move {
  animation: moveAnimation 1s ease-in-out infinite; /* Se repite infinitamente */
}
</style>
