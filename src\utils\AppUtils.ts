import moment from 'moment';
import { Notify } from 'quasar';
import type { ObjectError } from '../components/models';
import type { Empresa } from '../components/comun/empresaModel';

interface Response {
  error: string;
  mensaje: string;
}

export function mostrarNotificacion({
  color = 'red-5',
  textColor = 'white',
  icon = 'warning',
  message = 'Mensaje de notificación',
  timeout = 2000,
}) {
  Notify.create({
    color,
    textColor,
    icon,
    message,
    timeout,
  });
}

export function handleResponse(response: Response) {
  mostrarNotificacion({
    color: response.error === 'N' ? 'green-4' : 'red-5',
    icon: response.error === 'N' ? 'cloud_done' : 'warning',
    message: response.mensaje,
  });
}

export function deducirMensajeError(o_error: ObjectError) {
  let mensaje = '';
  let hubo = false;
  if (o_error.message) {
    mensaje =
      o_error.message === 'Network Error'
        ? 'La aplicación no logra conectarse con el servidor, revise si su dispositivo esta con internet o si el servidor esta disponible.'
        : o_error.message;
    hubo = true;
  }
  if (o_error.config) {
    if (o_error.config.url) {
      mensaje =
        mensaje +
        "<br><span style='color:red'>" +
        o_error.config.url +
        '</span>';
      hubo = true;
    }
  }
  if (hubo === false) {
    mensaje = JSON.stringify(o_error);
  }
  return mensaje;
}

export function formatearFechas(fechas: string[]) {
  if (fechas.length === 0) {
    return '';
  }

  const fechasFormateadas = [];
  let fechaInicial = moment(fechas[0], 'YYYY/MM/DD'); // Especifica el formato
  let fechaFinal = fechaInicial;

  for (let i = 1; i < fechas.length; i++) {
    const fecha = moment(fechas[i], 'YYYY/MM/DD'); // Especifica el formato

    if (
      fecha.diff(fechaFinal, 'days') === 1 &&
      fecha.month() === fechaFinal.month()
    ) {
      fechaFinal = fecha;
    } else {
      fechasFormateadas.push(formatoFechas(fechaInicial, fechaFinal));
      fechaInicial = fecha;
      fechaFinal = fecha;
    }
  }

  fechasFormateadas.push(formatoFechas(fechaInicial, fechaFinal));

  return fechasFormateadas.join(', ');
}

function formatoFechas(
  fechaInicial: moment.Moment,
  fechaFinal: moment.Moment
): string {
  if (fechaInicial.isSame(fechaFinal, 'day')) {
    return fechaInicial.format('DD/MM/YY');
  } else {
    return `${fechaInicial.format('DD')} - ${fechaFinal.format('DD/MM/YY')}`;
  }
}

export function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const formatoNocturno = (
  jornada: string,
  start: string,
  finish: string
): string => {
  return `${jornada.split(' ')[0]} (${start}) - ${
    jornada.split(' ')[1]
  } (${finish})`;
};

export const removeObjectFromArray = (
  arrayObj: Array<Empresa>,
  value: number
): boolean => {
  const index = arrayObj.findIndex((obj) => obj['index'] === value);
  if (index > -1) {
    arrayObj.splice(index, 1);
    return true;
  }
  return false;
};
