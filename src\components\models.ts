interface Transitional {
  silentJSONParsing: boolean;
  forcedJSONParsing: boolean;
  clarifyTimeoutError: boolean;
}

interface Params {
  id: string;
  clave: string;
}

interface Config {
  url: string;
  method: string;
  headers: Headers;
  params: Params;
  transformRequest: null[];
  transformResponse: null[];
  timeout: number;
  xsrfCookieName: string;
  xsrfHeaderName: string;
  maxContentLength: number;
  maxBodyLength: number;
  transitional: Transitional;
}

export interface ObjectError {
  message: string;
  name: string;
  stack: string;
  config: Config;
}

export interface Session {
  estaLogeado: boolean;
  currentURL: string;
  token: string | null;
  ruc: string;
  usuario: string;
  codigo: number;
  appCodigo: number;
  login: string;
  clave: string;
  alm_codigo: number;
  iso: string;
  pos: string;
  APP_ADMIN: number;
}

export interface DarkMode {
  darkMode: boolean;
}
export interface Opcion {
  label: string;
  valor: number;
}

export interface TokenDecoded {
  sub: string;
  password: string;
  exp: number;
}

export interface Operador {
  proveedor: string;
  producto: string;
}

export interface TransaccionObject {
  peticionRequerimiento: Transaccion;
}

interface Transaccion {
  tipoTransaccion: string;
  codigoProceso: string;
  monto: string;
  cajero: string;
  clave: string;
  tid: string;
  mid: string;
  proveedor: string;
  servicio: string;
  cuenta: string;
  autorizacion: string;
  referencia: string;
  lote: string;
  sbContrapartidaNombre: string;
  sbCedula: string;
  sbDireccion: string;
  sbTelefono: string;
  sbReferencia: string;
  sbReferenciaAdicional: string;
  sbCiudadCliente: string;
  sbCorreoDTV: string;
  modeloTerminal: string;
}

export interface DatosFactura {
  error: string;
  mensaje: string;
  objetos: ObjetosFactura;
}

export interface ObjetosFactura {
  clp_codigo: number;
  clp_cedruc: string;
  clp_descri: string;
  clp_calles: string;
  email: string;
  celular: string;
}

export interface RespuestaPvPTotal {
  error: string;
  mensaje: string;
  objetos: ObjetoPvPTotal[];
}

export interface ObjetoPvPTotal {
  total_pvp: number;
}

export interface RespuestaConciliaciones {
  error: string;
  mensaje: string;
  objetos: ObjetoConciliaciones[];
}

export interface ObjetoConciliaciones {
  codigo: number;
  grupo: string;
  cadena: string;
  comercio: string;
  proveedor: string;
  producto: string;
  lote: string;
  pos: string;
  cajero: string;
  fecha: string;
  hora: string;
  pvp: number;
  autorizacion: null;
  referencia: string;
  iso: string;
  telefono: string;
  codigo_proveedor: number;
  codigo_producto: number;
}

export interface FilasRecargas {
  codigo: number;
  grupo: string;
  cadena: string;
  comercio: string;
  proveedor: string;
  producto: string;
  lote: string;
  pos: string;
  cajero: string;
  fecha: string;
  hora: string;
  pvp: number;
  autorizacion: null;
  referencia: string;
  iso: string;
  telefono: string;
  codigo_proveedor: number;
  codigo_producto: number;
  detalle_factura: null;
  trn_codigo: null;
  recarga_exitosa: boolean;
  fpago: number;
}

export interface FilasRecargasFacturadas {
  codigo: number;
  grupo: string;
  cadena: string;
  comercio: string;
  proveedor: string;
  producto: string;
  lote: string;
  pos: string;
  cajero: string;
  fecha: string;
  hora: string;
  pvp: number;
  autorizacion: null;
  referencia: string;
  iso: string;
  telefono: string;
  codigo_proveedor: number;
  codigo_producto: number;
  detalle_factura: DetalleFactura;
  trn_codigo: null;
  recarga_exitosa: boolean;
}

export interface DetalleFactura {
  email: string;
  valor: number;
  ced_ruc: string;
  celular: string;
  recarga: string;
  factura: string;
  alm_codigo: string;
  clp_calles: string;
  clp_codigo: number;
  clp_descri: string;
  usu_codigo: number;
}

export interface RespuestaFactura {
  error: string;
  mensaje: string;
  objetos: ObjetoFact[];
}

interface ObjetoFact {
  codigo: number;
}

export interface SucursalesObject {
  alm_nomcom: string;
  pos: string;
}

export interface CuentasObject {
  bct_descri: string;
  bct_codigo: string;
}

export interface RepuestaTransferencia {
  Fecha: string;
  'Punto de atención': string;
  'Punto de venta': string;
  Identificación: string;
  Nombre: string;
  Valor: string;
  Motivo: string;
  'Cuenta acreditada': string;
  'Nombre beneficiario': string;
  Referencia: string;
}
