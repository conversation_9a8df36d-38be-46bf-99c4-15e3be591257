import { QTableProps } from 'quasar';

// import { FilasRecargas } from '../components/models';

export const columnasConciliaciones: QTableProps['columns'] = [
  {
    name: 'numeroFila',
    label: '#',
    field: (row) => row.rowIndex,
    align: 'left',
    sortable: false,
  },
  {
    name: 'codigo',
    align: 'left',
    label: 'ID',
    field: 'codigo',
  },
  {
    name: 'grupo',
    label: 'GRUPO',
    align: 'left',
    field: 'grupo',
    sortable: true,
  },
  {
    name: 'cadena',
    align: 'left',
    label: 'USUARIO',
    field: 'cadena',
    sortable: true,
  },
  {
    name: 'comercio',
    align: 'left',
    label: 'COMERCIO',
    field: 'comercio',
    sortable: true,
  },
  {
    name: 'proveedor',
    align: 'left',
    label: 'PROVEEDOR',
    field: 'proveedor',
    sortable: true,
  },
  {
    name: 'producto',
    align: 'left',
    label: 'PRODUCTO',
    field: 'producto',
    sortable: true,
  },
  {
    name: 'lote',
    align: 'left',
    label: 'LOTE',
    field: 'lote',
    sortable: true,
  },
  {
    name: 'pos',
    align: 'left',
    label: 'POS',
    field: 'pos',
    sortable: true,
  },
  {
    name: 'cajero',
    align: 'left',
    label: 'CAJERO',
    field: 'cajero',
    sortable: true,
  },
  {
    name: 'fecha',
    align: 'left',
    label: 'FECHA',
    field: 'fecha',
    sortable: true,
  },
  {
    name: 'hora',
    align: 'left',
    label: 'HORA',
    field: 'hora',
    sortable: true,
  },
  {
    name: 'pvp',
    align: 'left',
    label: 'PVP',
    field: 'pvp',
    sortable: true,
  },
  {
    name: 'autorizacion',
    align: 'left',
    label: 'AUTORIZACIÓN',
    field: 'autorizacion',
    sortable: true,
  },
  {
    name: 'referencia',
    align: 'left',
    label: 'REFERENCIA',
    field: 'referencia',
    sortable: true,
  },
  {
    name: 'iso',
    align: 'left',
    label: 'ISO',
    field: 'iso',
    sortable: true,
  },
  {
    name: 'telefono',
    align: 'left',
    label: 'TELÉFONO',
    field: 'telefono',
    sortable: true,
  },
  {
    name: 'codigo_proveedor',
    align: 'left',
    label: 'CODIGO PROVEEDOR',
    field: 'codigo_proveedor',
    sortable: true,
  },
  {
    name: 'codigo_producto',
    align: 'left',
    label: 'CODIGO PRODUCTO',
    field: 'codigo_producto',
    sortable: true,
  },
  {
    name: 'factura',
    align: 'left',
    label: 'FACTURA',
    field: 'factura',
    sortable: true,
  },
];

export const columnasRNF: QTableProps['columns'] = [
  {
    name: 'codigo',
    align: 'left',
    label: 'ID',
    field: (row) => row.codigo,
    format: (val) => `${val}`,
  },
  {
    name: 'detalle_factura',
    align: 'left',
    label: 'NOMBRE/RAZÓN SOCIAL',
    field: (row) => row.detalle_factura.clp_descri,
    sortable: true,
  },
  {
    name: 'grupo',
    label: 'GRUPO',
    align: 'left',
    field: 'grupo',
    sortable: true,
  },
  {
    name: 'cadena',
    align: 'left',
    label: 'USUARIO',
    field: 'cadena',
    sortable: true,
  },
  {
    name: 'comercio',
    align: 'left',
    label: 'COMERCIO',
    field: 'comercio',
    sortable: true,
  },
  {
    name: 'proveedor',
    align: 'left',
    label: 'PROVEEDOR',
    field: 'proveedor',
    sortable: true,
  },
  {
    name: 'producto',
    align: 'left',
    label: 'PRODUCTO',
    field: 'producto',
    sortable: true,
  },
  {
    name: 'lote',
    align: 'left',
    label: 'LOTE',
    field: 'lote',
    sortable: true,
  },
  {
    name: 'pos',
    align: 'left',
    label: 'POS',
    field: 'pos',
    sortable: true,
  },
  {
    name: 'cajero',
    align: 'left',
    label: 'CAJERO',
    field: 'cajero',
    sortable: true,
  },
  {
    name: 'fecha',
    align: 'left',
    label: 'FECHA',
    field: 'fecha',
    sortable: true,
  },
  {
    name: 'hora',
    align: 'left',
    label: 'HORA',
    field: 'hora',
    sortable: true,
  },
  {
    name: 'pvp',
    align: 'left',
    label: 'PVP',
    field: 'pvp',
    sortable: true,
  },
  {
    name: 'autorizacion',
    align: 'left',
    label: 'AUTORIZACIÓN',
    field: 'autorizacion',
    sortable: true,
  },
  {
    name: 'referencia',
    align: 'left',
    label: 'REFERENCIA',
    field: 'referencia',
    sortable: true,
  },
  {
    name: 'iso',
    align: 'left',
    label: 'ISO',
    field: 'iso',
    sortable: true,
  },
  {
    name: 'telefono',
    align: 'left',
    label: 'TELÉFONO',
    field: 'telefono',
    sortable: true,
  },
  {
    name: 'codigo_proveedor',
    align: 'left',
    label: 'CODIGO PROVEEDOR',
    field: 'codigo_proveedor',
    sortable: true,
  },
  {
    name: 'codigo_producto',
    align: 'left',
    label: 'TIPO DE RECARGA',
    field: 'codigo_producto',
    format: (val) => (val == 2 ? 'Tiempo Aire' : 'Paquete'),
    sortable: true,
  },
  {
    name: 'trn_codigo',
    align: 'left',
    label: 'TRN CODIGO',
    field: 'trn_codigo',
    sortable: true,
  },
  {
    name: 'recarga_exitosa',
    align: 'left',
    label: 'TRN CODIGO',
    field: 'recarga_exitosa',
    sortable: true,
  },
];
