<script setup lang="ts">
import { ref } from 'vue';
import { useAppStore } from '../../stores/app-store';
import ConciliacionesComponent from './ConciliacionesComponent.vue';
import ConciliacionesNFComponent from './ConciliacionesNFComponent.vue';
import ConciliacionesRNPComponent from './ConciliacionesRNPComponent.vue';

// Data
const tab = ref('');
const appStore = useAppStore();
</script>

<template>
  <div class="q-gutter-y-sm">
    <div class="q-pt-sm">
      <h4
        class="row text-uppercase justify-center content-center q-my-sm q-pb-md"
        style="font-family: 'Bebas Neue'"
      >
        <div class="q-pt-sm">Registro de conciliaciones</div>
      </h4>
    </div>

    <q-card flat>
      <q-tabs
        v-model="tab"
        dense
        :active-color="appStore.darkMode ? 'info' : 'primary'"
        :indicator-color="appStore.darkMode ? 'info' : 'primary'"
        style="font-family: 'Oswald'"
        align="justify"
        narrow-indicator
      >
        <q-tab name="facturadas" label="Recargas Facturadas" />
        <q-tab name="noFacturadas" label="Recargas no Facturadas" />
        <q-tab name="noExitosas" label="Recargas no Exitosas" />
      </q-tabs>

      <q-separator />

      <q-tab-panels v-model="tab" animated>
        <q-tab-panel name="facturadas">
          <ConciliacionesComponent />
        </q-tab-panel>
        <q-tab-panel name="noFacturadas">
          <ConciliacionesNFComponent />
        </q-tab-panel>
        <q-tab-panel name="noExitosas">
          <ConciliacionesRNPComponent />
        </q-tab-panel>
      </q-tab-panels>
    </q-card>
  </div>
</template>
