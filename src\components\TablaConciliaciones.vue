<script setup lang="ts">
import { computed } from 'vue';
import { exportFile, useQuasar } from 'quasar';
import { useAppStore } from '../stores/app-store';
import { columnasConciliaciones } from './columns';
import { formatearFecha } from '../services/useWorkDays';
import { obtenerNombreSucursal } from '../services/useRecargas';
import { ObjetoConciliaciones, SucursalesObject } from './models';

type ObjetoConciliacionesKeys = keyof ObjetoConciliaciones;

const date = defineModel<string>('date', { required: true });
const desde = defineModel<string>('desde', { required: true });
const hasta = defineModel<string>('hasta', { required: true });
const filter = defineModel<string>('filter', { required: true });
const titulo = defineModel<string>('titulo', { required: true });
const pvp_total = defineModel<number>('pvp_total', { required: true });
const blueModel = defineModel<boolean>('blueModel', { required: true });
const columnasVisibles = defineModel<string[]>('columnasVisibles', {
  required: true,
});
const filas = defineModel<ObjetoConciliaciones[]>('filas', {
  required: true,
});
const sucursales = defineModel<SucursalesObject[]>('sucursales', {
  required: true,
});

// Data
const $q = useQuasar();
const appStore = useAppStore();
const columnas = columnasConciliaciones;
const pagination = {
  page: 1,
  rowsPerPage: 0, // 0 means all rows
};

// Methods
const tituloTabla = computed(() => {
  return `Conciliación del ${formatearFecha(date.value)}`;
});

const tituloTablaFechas = computed(() => {
  return `Conciliación del ${formatearFecha(
    desde.value
  )} hasta el ${formatearFecha(hasta.value)}`;
});

const wrapCsvValue = (
  val: string | number | object,
  formatFn?: (
    value: string | number | object,
    row?: ObjetoConciliaciones
  ) => string | number | object,
  row?: ObjetoConciliaciones
): string => {
  let formatted = formatFn !== void 0 ? formatFn(val, row) : val;

  formatted =
    formatted === void 0 || formatted === null ? '' : String(formatted);

  formatted = formatted.split('"').join('""');

  return `"${formatted}"`;
};

const exportTable = () => {
  if (columnas) {
    // Obtener las columnas visibles
    const visibleColumns = columnas.filter((col) =>
      columnasVisibles.value.includes(col.name)
    );

    // Crear la primera fila del archivo CSV con las etiquetas de las columnas visibles
    const headerRow = visibleColumns.map((col) => wrapCsvValue(col.label));

    // Crear las filas de datos
    const dataRows = filas.value.map((row) =>
      visibleColumns
        .map((col) => {
          const field =
            typeof col.field === 'function'
              ? col.field(row)
              : row[col.field as ObjetoConciliacionesKeys];
          return wrapCsvValue(field, col.format, row);
        })
        .join(';')
    );

    // Combinar las filas en el contenido del archivo CSV
    const content = [headerRow].concat(dataRows).join('\r\n');

    const status = exportFile(
      `CONCILIACIONES-${appStore.empresa.nombre_comercial}-${date.value}.csv`,
      content,
      'text/csv'
    );

    if (status !== true) {
      $q.notify({
        message: 'El navegador denegó la descarga de archivos...',
        color: 'negative',
        icon: 'warning',
      });
    }
  }
};
</script>

<template>
  <div>
    <q-table
      flat
      bordered
      :rows="filas"
      :filter="filter"
      :columns="columnas"
      :title="tituloTabla"
      row-key="codigo_detalle"
      hide-bottom
      hide-pagination
      :rows-per-page-options="[0]"
      v-model:pagination="pagination"
      :visible-columns="columnasVisibles"
    >
      <template v-slot:top="props">
        <div
          :class="$q.screen.xs ? 'text-subtitle2 q-pb-sm' : 'text-subtitle1'"
          v-if="filas.length > 0 && !blueModel"
        >
          {{ tituloTabla }}
          <br />
          <span v-if="titulo != 'Total de recargas no exitosas'">
            {{ `${titulo}: ${pvp_total}` }}
          </span>
        </div>

        <div
          :class="$q.screen.xs ? 'text-subtitle2 q-pb-sm' : 'text-subtitle1'"
          v-if="filas.length > 0 && blueModel && desde && hasta"
        >
          {{ tituloTablaFechas }}
          <br />
          <span v-if="titulo != 'Total de recargas no exitosas'">
            {{ `${titulo}: ${pvp_total}` }}
          </span>
        </div>
        <q-space />
        <q-select
          v-model="columnasVisibles"
          multiple
          outlined
          dense
          options-dense
          :display-value="$q.lang.table.columns"
          emit-value
          map-options
          :options="columnas"
          option-value="name"
          options-cover
          style="min-width: 150px"
          class="q-mr-sm"
        />
        <q-btn
          color="primary"
          icon-right="archive"
          label="Exportar a csv"
          no-caps
          @click="exportTable"
          :disable="filas.length === 0"
        />
        <q-btn
          v-if="filas.length > 0"
          flat
          round
          dense
          :icon="props.inFullscreen ? 'fullscreen_exit' : 'fullscreen'"
          @click="props.toggleFullscreen"
          class="q-ml-md"
        />
      </template>
      <template v-slot:body-cell-numeroFila="props">
        <q-td>
          <span>{{ props.rowIndex + 1 }}</span>
        </q-td>
      </template>
      <template v-slot:body-cell-pos="props">
        <q-td :props="props">
          {{ props.value }}

          <q-tooltip class="text-body2" :offset="[10, 10]">
            {{ obtenerNombreSucursal(props.value, sucursales) }}
          </q-tooltip>
        </q-td>
      </template>
    </q-table>
  </div>
</template>
